<?php
/*
Plugin Name: Q-Xero
Plugin URI:
Description: A powerful WordPress plugin that seamlessly integrates Xero accounting with Formidable Forms, enabling efficient data synchronization between your forms and Xero API. Enhance your accounting workflows with automated form-to-Xero mapping, custom field synchronization, and user-friendly import features.
Version: 1.0.0
Author: Q-Ai
Author URI:
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: qx
*/

// Check if WordPress is loaded
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Ensure WordPress functions are available
if (!function_exists('add_action')) {
    return; // Exit if WordPress functions are not available
}

// Make sure WordPress core files are included
require_once(ABSPATH . 'wp-load.php');
require_once(ABSPATH . 'wp-admin/includes/plugin.php');
require_once(ABSPATH . 'wp-includes/pluggable.php');
require_once(ABSPATH . 'wp-includes/functions.php');

// Add this near the top of the file, after the plugin header
add_filter('cron_schedules', 'add_fifteen_minute_cron_schedule');

function add_fifteen_minute_cron_schedule($schedules)
{
    $schedules['fifteen_minutes'] = array(
        'interval' => 900,   // 15 minutes in seconds
        'display' => __('Every 15 Minutes', 'qxero')
    );
    return $schedules;
}

class QXero
{
    private $client_id;
    private $client_secret;
    private $redirect_uri;
    private $scopes = [];
    private $notification_messages = [];
    public function __construct()
    {
        // Ensure WordPress functions are available
        if (function_exists('add_action')) {
            add_action('admin_menu', [$this, 'add_settings_page']);
            add_action('admin_init', [$this, 'register_settings']);
            add_action('init', [$this, 'handle_oauth_redirect']);
            add_action('frm_after_create_entry', [$this, 'handle_form_submission'], 10, 2);
            add_action('frm_after_update_entry', [$this, 'handle_form_submission'], 10, 2);
            add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_styles']);
            add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_scripts']);

            // Add AJAX handlers
            add_action('wp_ajax_get_form_fields', [$this, 'ajax_get_form_fields']);
        }

        // Ensure WordPress functions are available
        if (function_exists('get_option')) {
            $this->client_id = get_option('xero_client_id');
            $this->client_secret = get_option('xero_client_secret');
            $this->redirect_uri = get_option('xero_redirect_uri');
            $this->scopes = get_option('xero_scopes', []);
        }

        if (function_exists('add_shortcode')) {
            add_shortcode('xero_auth_button', [$this, 'auth_button']);
            add_shortcode('xero_sync_button', [$this, 'sync_data']);
        }

        // Add polling schedule
        add_action('init', function () {
            if (!wp_next_scheduled('xero_poll_updates')) {
                wp_schedule_event(time(), 'fifteen_minutes', 'xero_poll_updates');
            }
        });
        add_action('xero_poll_updates', [$this, 'poll_xero_updates']);

        // Register custom cron interval
        add_filter('cron_schedules', [$this, 'add_cron_interval']);

        // Add handler for manual update
        add_action('admin_post_xero_manual_update', [$this, 'handle_manual_update']);

        // Add admin notices
        add_action('admin_notices', [$this, 'show_admin_notices']);
    }

    /**
     * Enqueue admin styles
     *
     * @param string $hook The current admin page
     * @return void
     */
    public function enqueue_admin_styles($hook)
    {
        // Only load on our plugin page
        if (strpos($hook, 'q-xero') !== false) {
            wp_enqueue_style('qx-styles', plugin_dir_url(__FILE__) . 'assets/css/qx-styles.css', [], '1.0.0');
        }
    }

    /**
     * Enqueue frontend scripts
     *
     * @return void
     */
    public function enqueue_frontend_scripts()
    {
        // Add the ajaxurl variable for frontend AJAX requests
        wp_enqueue_script('jquery');

        // Add inline script to define ajaxurl
        wp_add_inline_script('jquery', 'var ajaxurl = "' . admin_url('admin-ajax.php') . '";');
    }

    public function add_settings_page()
    {
        if (function_exists('add_menu_page')) {
            add_menu_page(
                'Q-Xero',
                'Q-Xero',
                'manage_options',
                'q-xero',
                [$this, 'settings_page_content'],
                'dashicons-rest-api'
            );
        }
    }

    public function register_settings()
    {
        if (function_exists('register_setting')) {
            $settings = [
                'xero_client_id',
                'xero_client_secret',
                'xero_redirect_uri',
                'xero_scopes',
                'xero_forms_mapping',
                'xero_polling_enabled',
                'xero_polling_interval',
                'xero_api_base_url',
                'xero_batch_size',
                'xero_api_page_size',
                'xero_timeout',
            ];

            foreach ($settings as $setting) {
                register_setting('xero_settings_group', $setting);
            }

            // Check if form mappings exist, if not create default mapping
            $existing_mappings = get_option('xero_forms_mapping');
            if (empty($existing_mappings)) {
                $default_mapping = [
                    [
                        'form_id' => '',
                        'endpoint' => 'Contacts',
                        'field_mappings' => json_encode([
                            'Name' => '',
                            'EmailAddress' => '',
                            'Phones.PhoneNumber' => '',
                            'Addresses.AddressLine1' => ''
                        ]),
                        'required_fields' => 'Name,EmailAddress',
                        'unique_id_field' => '',
                        'button_text' => 'Import from Xero',
                        'include_in_fetch' => 1
                    ]
                ];
                update_option('xero_forms_mapping', $default_mapping);
            }
        }
    }

    public function settings_page_content()
    {
?>
        <div class="wrap">
            <h1>Q Xero Integration Settings</h1>

            <!-- Add Manual Update Button -->
            <div class="xero-manual-update">
                <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" id="xero-manual-update-form">
                    <?php wp_nonce_field('xero_manual_update_nonce'); ?>
                    <input type="hidden" name="action" value="xero_manual_update">
                    <button type="submit" class="button button-primary" id="xero-manual-update-button">
                        <span class="dashicons dashicons-update"></span>
                        <span class="button-text">Manual Update</span>
                    </button>
                </form>
            </div>

            <script type="text/javascript">
                jQuery(document).ready(function($) {
                    $('#xero-manual-update-form').on('submit', function() {
                        var $button = $('#xero-manual-update-button');
                        $button.addClass('loading');
                        $button.find('.button-text').text('Updating...');
                        $button.prop('disabled', true);
                    });
                });
            </script>

            <!-- Add tab navigation -->
            <nav class="nav-tab-wrapper">
                <a href="#general" class="nav-tab nav-tab-active">
                    <span class="dashicons dashicons-admin-generic"></span> General Settings
                </a>
                <a href="#form-mappings" class="nav-tab">
                    <span class="dashicons dashicons-list-view"></span> Form Mappings
                </a>
            </nav>

            <form method="post" action="options.php">
                <?php
                settings_fields('xero_settings_group');
                do_settings_sections('xero_settings_group');
                ?>
                <div id="general" class="tab-content active">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="xero_client_id">Client ID</label></th>
                            <td>
                                <input type="text" id="xero_client_id" name="xero_client_id"
                                    value="<?php echo esc_attr(get_option('xero_client_id')); ?>" class="regular-text" />
                                <p class="description">Enter your Xero Client ID, which is required for API authentication.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_client_secret">Client Secret</label></th>
                            <td>
                                <input type="password" id="xero_client_secret" name="xero_client_secret"
                                    value="<?php echo esc_attr(get_option('xero_client_secret')); ?>" class="regular-text" />
                                <p class="description">Provide your Xero Client Secret, which is used in conjunction with the
                                    Client ID for secure access.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_redirect_uri">Redirect URL</label></th>
                            <td>
                                <input type="text" id="xero_redirect_uri" name="xero_redirect_uri"
                                    value="<?php echo esc_attr(get_option('xero_redirect_uri')); ?>" class="regular-text" />
                                <p class="description">Specify the Redirect URL where users will be sent after authentication.
                                    This must match the URL set in your Xero app settings.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label>Scopes</label></th>
                            <td>
                                <div id="scopes-container">
                                    <?php
                                    $scopes = get_option('xero_scopes', []);
                                    if (!is_array($scopes)) {
                                        $scopes = [];
                                    }

                                    if (!empty($scopes)) {
                                        foreach ($scopes as $scope): ?>
                                            <div class="scope-item">
                                                <input type="text" name="xero_scopes[]" value="<?php echo esc_attr($scope); ?>"
                                                    class="regular-text" />
                                                <button type="button" class="button remove-scope">Remove</button>
                                            </div>
                                        <?php endforeach;
                                    } else {
                                        // Add default scopes if none exist
                                        $default_scopes = ['accounting.transactions', 'accounting.contacts', 'offline_access', 'openid', 'profile', 'email'];
                                        foreach ($default_scopes as $scope): ?>
                                            <div class="scope-item">
                                                <input type="text" name="xero_scopes[]" value="<?php echo esc_attr($scope); ?>"
                                                    class="regular-text" />
                                                <button type="button" class="button remove-scope">Remove</button>
                                            </div>
                                    <?php endforeach;
                                    }
                                    ?>
                                </div>
                                <button type="button" class="button add-scope">Add Scope</button>
                                <p class="description">Define the scopes required for your application. Scopes determine the
                                    level of access your application has to Xero data.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><label for="xero_api_base_url">API Base URL</label></th>
                            <td>
                                <input type="text" id="xero_api_base_url" name="xero_api_base_url"
                                    value="<?php echo esc_attr(get_option('xero_api_base_url')); ?>" class="regular-text" />
                                <p class="description">Enter the base URL for the Xero API. The default is usually sufficient
                                    unless you are using a custom endpoint.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><label for="xero_polling_enabled">Enable Polling</label></th>
                            <td>
                                <input type="checkbox" id="xero_polling_enabled" name="xero_polling_enabled" value="1" <?php checked(get_option('xero_polling_enabled'), 1); ?> />
                                <p class="description">Check this box to enable automatic polling for updates from Xero. This
                                    will allow your application to stay in sync with Xero data.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_polling_interval">Polling Interval</label></th>
                            <td>
                                <select id="xero_polling_interval" name="xero_polling_interval">
                                    <option value="900" <?php selected(get_option('xero_polling_interval'), 900); ?>>15 minutes
                                    </option>
                                    <option value="1800" <?php selected(get_option('xero_polling_interval'), 1800); ?>>30
                                        minutes</option>
                                    <option value="3600" <?php selected(get_option('xero_polling_interval'), 3600); ?>>1 hour
                                    </option>
                                    <option value="7200" <?php selected(get_option('xero_polling_interval'), 7200); ?>>2 hours
                                    </option>
                                </select>
                                <p class="description">Select the interval for polling updates from Xero. Choose a frequency
                                    that balances performance and data freshness.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_batch_size">Import Batch Size</label></th>
                            <td>
                                <select id="xero_batch_size" name="xero_batch_size">
                                    <option value="25" <?php selected(get_option('xero_batch_size'), 25); ?>>25 records</option>
                                    <option value="50" <?php selected(get_option('xero_batch_size'), 50); ?>>50 records</option>
                                    <option value="100" <?php selected(get_option('xero_batch_size'), 100); ?>>100 records
                                    </option>
                                    <option value="200" <?php selected(get_option('xero_batch_size'), 200); ?>>200 records
                                    </option>
                                </select>
                                <p class="description">Set the number of records to process in each batch during large imports.
                                    Smaller batch sizes use less memory but take longer to complete.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_api_page_size">API Page Size</label></th>
                            <td>
                                <select id="xero_api_page_size" name="xero_api_page_size">
                                    <option value="100" <?php selected(get_option('xero_api_page_size'), 10); ?>>10 records
                                    </option>
                                    <option value="200" <?php selected(get_option('xero_api_page_size'), 25); ?>>25 records
                                    </option>
                                    <option value="500" <?php selected(get_option('xero_api_page_size'), 50); ?>>50 records
                                    </option>
                                    <option value="1000" <?php selected(get_option('xero_api_page_size'), 100); ?>>100 records
                                    </option>
                                    <option value="2000" <?php selected(get_option('xero_api_page_size'), 200); ?>>200 records
                                    </option>
                                </select>
                                <p class="description">Set the number of records to fetch from Xero API in each request.
                                    This controls how many records are retrieved at once from Xero. Use larger values for faster
                                    imports of large datasets.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="xero_timeout">API Request Timeout</label></th>
                            <td>
                                <select id="xero_timeout" name="xero_timeout">
                                    <option value="30" <?php selected(get_option('xero_timeout'), 30); ?>>30 seconds</option>
                                    <option value="60" <?php selected(get_option('xero_timeout'), 60); ?>>60 seconds</option>
                                    <option value="120" <?php selected(get_option('xero_timeout'), 120); ?>>120 seconds</option>
                                    <option value="180" <?php selected(get_option('xero_timeout'), 180); ?>>180 seconds</option>
                                    <option value="300" <?php selected(get_option('xero_timeout'), 300); ?>>300 seconds (5 min)
                                    </option>
                                    <option value="600" <?php selected(get_option('xero_timeout'), 600); ?>>600 seconds (10 min)
                                    </option>
                                </select>
                                <p class="description">Set the timeout for API requests to Xero. Increase this value for large
                                    data sets or slow connections.</p>
                            </td>
                        </tr>



                        <tr>
                            <th scope="row">API Rate Limit Status</th>
                            <td>
                                <?php
                                $rate_limit_hit = get_option('xero_rate_limit_hit', 0);
                                $retry_after = get_option('xero_rate_limit_retry_after', 0);
                                $rate_limit_problem = get_option('xero_rate_limit_problem', '');
                                if ($rate_limit_hit > 0) {
                                    $current_time = time();
                                    $time_passed = $current_time - $rate_limit_hit;

                                    if ($time_passed < $retry_after) {
                                        $wait_time = $retry_after - $time_passed;
                                        $formatted_wait_time = $this->format_time_duration($wait_time);
                                        echo '<div class="notice notice-error inline"><p><strong>Rate Limited:</strong> You have hit the Xero ' . esc_html($rate_limit_problem) . ' API rate limit. Please wait ' . esc_html($formatted_wait_time) . ' before making more requests.</p></div>';
                                    } else {
                                        echo '<div class="notice notice-success inline"><p><strong>Status:</strong> API rate limits are normal. You can make requests to the Xero API.</p></div>';
                                        // Clear the rate limit
                                        delete_option('xero_rate_limit_hit');
                                        delete_option('xero_rate_limit_retry_after');
                                        delete_option('xero_rate_limit_problem');
                                    }
                                } else {
                                    echo '<div class="notice notice-success inline"><p><strong>Status:</strong> API rate limits are normal. You can make requests to the Xero API.</p></div>';
                                }
                                ?>
                                <p class="description">Xero has various rate limits that restrict how many API requests you can
                                    make in a given time period. If you hit these limits, you'll need to wait before making more
                                    requests.</p>
                            </td>
                        </tr>

                    </table>
                </div>

                <div id="form-mappings" class="tab-content">
                    <h3>Form Mappings</h3>
                    <div id="form_mappings_container">
                        <?php
                        $form_mappings = get_option('xero_forms_mapping', []);
                        if (!is_array($form_mappings)) {
                            $form_mappings = [];
                        }
                        foreach ($form_mappings as $index => $mapping) {
                        ?>
                            <div class="form-mapping-card" data-index="<?php echo $index; ?>">
                                <div class="card-header">
                                    <h4>Form Mapping <?php echo $index + 1; ?></h4>
                                    <button type="button" class="button-link toggle-card">
                                        <span class="dashicons dashicons-arrow-down"></span>
                                    </button>
                                </div>

                                <fieldset class="form-mapping-fields">
                                    <div class="form-section basic-settings">
                                        <h5>Basic Settings</h5>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Form</label>
                                                <?php
                                                // Get all Formidable forms
                                                if (class_exists('FrmForm')) {
                                                    $forms = FrmForm::get_published_forms();
                                                ?>
                                                    <select name="xero_forms_mapping[<?php echo $index; ?>][form_id]"
                                                        class="regular-text">
                                                        <option value="">Select a Form</option>
                                                        <?php
                                                        foreach ($forms as $form) {
                                                            $selected = ($mapping['form_id'] == $form->id) ? 'selected' : '';
                                                            echo '<option value="' . esc_attr($form->id) . '" ' . $selected . '>' .
                                                                esc_html($form->name) . ' (ID: ' . esc_html($form->id) . ')</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                    <p class="description">Select the Formidable form you want to map to Xero.</p>
                                                <?php
                                                } else {
                                                    echo '<div class="notice notice-error inline"><p>Formidable Forms plugin is not active. Please install and activate it first.</p></div>';
                                                }
                                                ?>
                                            </div>
                                            <div class="form-group">
                                                <label>Endpoint</label>
                                                <input type="text" name="xero_forms_mapping[<?php echo $index; ?>][endpoint]"
                                                    value="<?php echo esc_attr($mapping['endpoint']); ?>" class="regular-text" />
                                                <p class="description">Specify the Xero API endpoint that this form will interact
                                                    with.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section mapping-settings">
                                        <h5>Field Mappings</h5>
                                        <div class="field-mapping-container">
                                            <div class="field-mapping-table">
                                                <div class="field-mapping-header">
                                                    <div class="field-mapping-column">Form Field</div>
                                                    <div class="field-mapping-column">Xero Field</div>
                                                    <div class="field-mapping-column actions">Actions</div>
                                                </div>
                                                <div class="field-mappings-rows">
                                                    <?php
                                                    $field_mappings = [];
                                                    if (!empty($mapping['field_mappings'])) {
                                                        $field_mappings = json_decode($mapping['field_mappings'], true);
                                                    }

                                                    if ($field_mappings) {
                                                        foreach ($field_mappings as $xero_field => $form_field) {
                                                    ?>
                                                            <div class="field-mapping-row">
                                                                <div class="field-mapping-column">
                                                                    <select class="form-field-select"
                                                                        data-form-id="<?php echo esc_attr($mapping['form_id']); ?>">
                                                                        <option value="">Select Form Field</option>
                                                                        <?php
                                                                        // Fetch fields for the selected form
                                                                        $form_fields = FrmField::get_all_for_form($mapping['form_id']);
                                                                        foreach ($form_fields as $field) {
                                                                            $selected = ($form_field == $field->id) ? 'selected' : '';
                                                                            echo '<option value="' . esc_attr($field->id) . '" ' . $selected . '>' . esc_html($field->name) . '</option>';
                                                                        }
                                                                        ?>
                                                                    </select>
                                                                </div>
                                                                <div class="field-mapping-column">
                                                                    <select class="xero-field-select">
                                                                        <option value="">Select Xero Field</option>
                                                                        <optgroup label="Basic Info">
                                                                            <option value="ContactID">Contact ID</option>
                                                                            <option value="ContactNumber">Contact Number</option>
                                                                            <option value="AccountNumber">Account Number</option>
                                                                            <option value="Name">Name</option>
                                                                            <option value="FirstName">First Name</option>
                                                                            <option value="LastName">Last Name</option>
                                                                            <option value="FirstName+LastName" <?php if (isset($xero_field)) selected($xero_field, 'FirstName+LastName'); ?>>First Name + Last Name</option>
                                                                            <option value="EmailAddress">Email Address</option>
                                                                            <option value="SkypeUserName">Skype User Name</option>
                                                                            <option value="Website">Website</option>
                                                                            <option value="DefaultCurrency">Default Currency</option>
                                                                            <option value="ContactStatus">Contact Status</option>
                                                                            <option value="IsSupplier">Is Supplier</option>
                                                                            <option value="IsCustomer">Is Customer</option>
                                                                        </optgroup>
                                                                        <optgroup label="Addresses">
                                                                            <option value="Addresses.AddressType">Address Type</option>
                                                                            <option value="Addresses.AddressLine1">Address Line 1</option>
                                                                            <option value="Addresses.AddressLine2">Address Line 2</option>
                                                                            <option value="Addresses.AddressLine3">Address Line 3</option>
                                                                            <option value="Addresses.AddressLine4">Address Line 4</option>
                                                                            <option value="Addresses.City">City</option>
                                                                            <option value="Addresses.Region">Region</option>
                                                                            <option value="Addresses.PostalCode">Postal Code</option>
                                                                            <option value="Addresses.Country">Country</option>
                                                                            <option value="Addresses.AttentionTo">Attention To</option>
                                                                        </optgroup>
                                                                        <optgroup label="Phones">
                                                                            <option value="Phones.PhoneType">Phone Type</option>
                                                                            <option value="Phones.PhoneNumber">Phone Number</option>
                                                                            <option value="Phones.PhoneAreaCode">Phone Area Code</option>
                                                                            <option value="Phones.PhoneCountryCode">Phone Country Code</option>
                                                                        </optgroup>
                                                                        <optgroup label="Contact Persons">
                                                                            <option value="ContactPersons.FirstName">Contact Person First Name</option>
                                                                            <option value="ContactPersons.LastName">Contact Person Last Name</option>
                                                                            <option value="ContactPersons.EmailAddress">Contact Person Email</option>
                                                                            <option value="ContactPersons.IncludeInEmails">Include In Emails</option>
                                                                        </optgroup>
                                                                        <optgroup label="Financial/Other">
                                                                            <option value="BankAccountDetails">Bank Account Details</option>
                                                                            <option value="TaxNumber">Tax Number</option>
                                                                            <option value="AccountsReceivableTaxType">Accounts Receivable Tax Type</option>
                                                                            <option value="AccountsPayableTaxType">Accounts Payable Tax Type</option>
                                                                            <option value="Discount">Discount</option>
                                                                            <option value="BrandingThemeID">Branding Theme ID</option>
                                                                            <option value="BrandingThemeName">Branding Theme Name</option>
                                                                            <option value="PurchasesDefaultAccountCode">Purchases Default Account Code</option>
                                                                            <option value="SalesDefaultAccountCode">Sales Default Account Code</option>
                                                                            <option value="PaymentTerms.Bills.Day">Payment Terms Bills Day</option>
                                                                            <option value="PaymentTerms.Bills.Type">Payment Terms Bills Type</option>
                                                                            <option value="PaymentTerms.Sales.Day">Payment Terms Sales Day</option>
                                                                            <option value="PaymentTerms.Sales.Type">Payment Terms Sales Type</option>
                                                                            <option value="UpdatedDateUTC">Updated Date UTC</option>
                                                                            <option value="HasAttachments">Has Attachments</option>
                                                                        </optgroup>
                                                                        <optgroup label="Contact Persons (1-5)">
                                                                            <option value="ContactPerson1.FirstName">Contact Person 1 First Name</option>
                                                                            <option value="ContactPerson1.LastName">Contact Person 1 Last Name</option>
                                                                            <option value="ContactPerson1.EmailAddress">Contact Person 1 Email</option>
                                                                            <option value="ContactPerson1.IncludeInEmails">Contact Person 1 Include In Emails</option>
                                                                            <option value="ContactPerson2.FirstName">Contact Person 2 First Name</option>
                                                                            <option value="ContactPerson2.LastName">Contact Person 2 Last Name</option>
                                                                            <option value="ContactPerson2.EmailAddress">Contact Person 2 Email</option>
                                                                            <option value="ContactPerson2.IncludeInEmails">Contact Person 2 Include In Emails</option>
                                                                            <option value="ContactPerson3.FirstName">Contact Person 3 First Name</option>
                                                                            <option value="ContactPerson3.LastName">Contact Person 3 Last Name</option>
                                                                            <option value="ContactPerson3.EmailAddress">Contact Person 3 Email</option>
                                                                            <option value="ContactPerson3.IncludeInEmails">Contact Person 3 Include In Emails</option>
                                                                            <option value="ContactPerson4.FirstName">Contact Person 4 First Name</option>
                                                                            <option value="ContactPerson4.LastName">Contact Person 4 Last Name</option>
                                                                            <option value="ContactPerson4.EmailAddress">Contact Person 4 Email</option>
                                                                            <option value="ContactPerson4.IncludeInEmails">Contact Person 4 Include In Emails</option>
                                                                            <option value="ContactPerson5.FirstName">Contact Person 5 First Name</option>
                                                                            <option value="ContactPerson5.LastName">Contact Person 5 Last Name</option>
                                                                            <option value="ContactPerson5.EmailAddress">Contact Person 5 Email</option>
                                                                            <option value="ContactPerson5.IncludeInEmails">Contact Person 5 Include In Emails</option>
                                                                        </optgroup>
                                                                        <optgroup label="Batch Payments">
                                                                            <option value="BatchPayments.BankAccountNumber">Batch Payments Bank Account Number</option>
                                                                            <option value="BatchPayments.BankAccountName">Batch Payments Bank Account Name</option>
                                                                            <option value="BatchPayments.Details">Batch Payments Details</option>
                                                                            <option value="BatchPayments.Code">Batch Payments Code</option>
                                                                            <option value="BatchPayments.Reference">Batch Payments Reference</option>
                                                                        </optgroup>
                                                                        <optgroup label="Tracking Categories">
                                                                            <option value="SalesTrackingCategory1.Name">Sales Tracking Category 1 Name</option>
                                                                            <option value="SalesTrackingOption1.Name">Sales Tracking Option 1 Name</option>
                                                                            <option value="SalesTrackingCategory2.Name">Sales Tracking Category 2 Name</option>
                                                                            <option value="SalesTrackingOption2.Name">Sales Tracking Option 2 Name</option>
                                                                            <option value="PurchaseTrackingCategory1.Name">Purchase Tracking Category 1 Name</option>
                                                                            <option value="PurchaseTrackingOption1.Name">Purchase Tracking Option 1 Name</option>
                                                                            <option value="PurchaseTrackingCategory2.Name">Purchase Tracking Category 2 Name</option>
                                                                            <option value="PurchaseTrackingOption2.Name">Purchase Tracking Option 2 Name</option>
                                                                        </optgroup>
                                                                        <optgroup label="Balances">
                                                                            <option value="Balances.AccountsReceivable.Outstanding">Accounts Receivable Outstanding</option>
                                                                            <option value="Balances.AccountsReceivable.Overdue">Accounts Receivable Overdue</option>
                                                                            <option value="Balances.AccountsPayable.Outstanding">Accounts Payable Outstanding</option>
                                                                            <option value="Balances.AccountsPayable.Overdue">Accounts Payable Overdue</option>
                                                                        </optgroup>
                                                                        <optgroup label="Other">
                                                                            <option value="XeroNetworkKey">Xero Network Key</option>
                                                                            <option value="ContactGroupId">Contact Group ID</option>
                                                                            <option value="BrandingThemeId">Branding Theme ID</option>
                                                                            <option value="BrandingThemeName">Branding Theme Name</option>
                                                                            <option value="TenantId">Tenant ID</option>
                                                                        </optgroup>
                                                                    </select>
                                                                    <script>
                                                                        jQuery(document).ready(function($) {
                                                                            // Set the selected value for this specific row
                                                                            var $currentRow = $('.field-mapping-row').eq(<?php echo array_search($xero_field, array_keys($field_mappings)); ?>);
                                                                            $currentRow.find('.xero-field-select').val('<?php echo esc_js($xero_field); ?>');
                                                                        });
                                                                    </script>
                                                                </div>
                                                                <div class="field-mapping-column actions">
                                                                    <button type="button" class="button remove-mapping">
                                                                        <span class="dashicons dashicons-trash"></span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                    <?php
                                                        }
                                                    }
                                                    ?>
                                                    <script>
                                                        jQuery(document).ready(function($) {
                                                            // Initialize field mappings JSON after all rows are loaded
                                                            setTimeout(function() {
                                                                $('.field-mapping-container').each(function() {
                                                                    updateFieldMappingsJson($(this));
                                                                });
                                                            }, 500);
                                                        });
                                                    </script>
                                                </div>
                                            </div>
                                            <button type="button" class="button add-field-mapping">
                                                <span class="dashicons dashicons-plus-alt"></span> Add Field Mapping
                                            </button>
                                            <button type="button" class="button test-ajax-call" style="margin-left: 10px; background: #ff6b6b; color: white;">
                                                Test AJAX Call
                                            </button>
                                            <input type="hidden" name="xero_forms_mapping[<?php echo $index; ?>][field_mappings]"
                                                class="field-mappings-json"
                                                value="<?php echo esc_attr($mapping['field_mappings']); ?>"
                                                data-original="<?php echo esc_attr($mapping['field_mappings']); ?>" />
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Required Fields</label>
                                                <input type="text" name="xero_forms_mapping[<?php echo $index; ?>][required_fields]"
                                                    value="<?php echo esc_attr($mapping['required_fields'] ?? ''); ?>"
                                                    class="regular-text" />
                                                <p class="description">List any fields that are required for submission to Xero,
                                                    separated by commas.</p>
                                            </div>
                                            <div class="form-group">
                                                <label>Unique ID Field</label>
                                                <select name="xero_forms_mapping[<?php echo $index; ?>][unique_id_field]"
                                                    class="regular-text">
                                                    <option value="">Select Unique ID Field</option>
                                                    <?php
                                                    // Fetch fields for the selected form
                                                    $form_fields = FrmField::get_all_for_form($mapping['form_id']);
                                                    foreach ($form_fields as $field) {
                                                        $selected = ($mapping['unique_id_field'] == $field->id) ? 'selected' : '';
                                                        echo '<option value="' . esc_attr($field->id) . '" ' . $selected . '>' . esc_html($field->name) . '</option>';
                                                    }
                                                    ?>
                                                </select>
                                                <p class="description">Specify the field that contains the unique identifier for
                                                    each entry.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section additional-settings">
                                        <h5>Additional Settings</h5>
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label>Button Text</label>
                                                <input type="text" name="xero_forms_mapping[<?php echo $index; ?>][button_text]"
                                                    value="<?php echo esc_attr($mapping['button_text'] ?? 'Import from Xero'); ?>"
                                                    class="regular-text" />
                                                <p class="description">Customize the text displayed on the button for importing data
                                                    from Xero.</p>
                                            </div>
                                            <div class="form-group checkbox-group">
                                                <label>
                                                    <input type="checkbox"
                                                        name="xero_forms_mapping[<?php echo $index; ?>][include_in_fetch]" value="1"
                                                        <?php checked(isset($mapping['include_in_fetch']) && $mapping['include_in_fetch']); ?> />
                                                    Include in Fetch
                                                </label>
                                                <p class="description">Check this box to include this mapping in the data fetch
                                                    process from Xero.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section shortcode-section">
                                        <h5>Shortcode</h5>
                                        <div class="form-row">
                                            <code
                                                class="shortcode-display">[xero_sync_button form_id="<?php echo esc_attr($mapping['form_id']); ?>" button_text="<?php echo esc_attr($mapping['button_text'] ?? 'Import from Xero'); ?>"]</code>
                                            <p class="description">Use this shortcode to display the sync button on the front end of
                                                your site.</p>
                                        </div>
                                    </div>
                                </fieldset>

                                <div class="card-footer">
                                    <button type="button" class="button xero-remove-card">
                                        <span class="dashicons dashicons-trash"></span> Remove
                                    </button>
                                </div>
                            </div>
                        <?php
                        }
                        ?>
                    </div>
                    <button type="button" class="button xero-add-card"><span class="dashicons dashicons-plus-alt"></span> Add
                        New Form Mapping</button>
                </div>

                <?php if (function_exists('submit_button'))
                    submit_button(); ?>
            </form>

            <h4>Shortcodes</h4>
            <p>Use the following shortcodes to display the buttons on the front end:</p>
            <ul>
                <li><strong>Authentication Button:</strong> <code id="auth_shortcode">[xero_auth_button]</code></li>
            </ul>



            <!-- Modal for import progress and confirmation -->
            <div id="xero-import-modal" class="xero-modal">
                <div class="xero-modal-content">
                    <div class="xero-modal-header">
                        <span class="xero-modal-close">&times;</span>
                        <h3>Import Progress</h3>
                    </div>
                    <div class="xero-modal-body">
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: 0%;"></div>
                            </div>
                            <div class="progress-text">Starting import...</div>
                        </div>
                        <div class="import-details">
                            <p><strong>Status:</strong> <span class="import-status">Initializing...</span></p>
                            <p><strong>Records Synced:</strong> <span class="records-synced">0</span></p>
                            <p><strong>Current Batch:</strong> <span class="current-batch">0</span></p>
                            <p><strong>Rate Limit Status:</strong> <span class="rate-limit-status">Checking...</span></p>
                            <div class="modal-actions">
                                <button type="button" class="button cancel-import-button">Cancel Import</button>
                            </div>
                        </div>
                        <div id="rate-limit-info" style="display: none;">
                            <div class="rate-limit-warning">
                                <p><strong>Rate Limit Warning:</strong></p>
                                <p class="rate-limit-message"></p>
                                <p class="rate-limit-timer"></p>
                            </div>
                        </div>
                        <div id="modal-message"></div>
                    </div>
                </div>
            </div>
            <!-- Powered by Q-Ai footer -->
            <div class="qx-footer"
                style="margin-top: 30px; padding: 15px 0; text-align: center; border-top: 1px solid var(--qx-border-light);">
                <p style="color: var(--qx-text-light); font-size: 14px;">Powered by <strong
                        style="color: var(--qx-primary);">Q-Ai</strong></p>
            </div>

        </div>
        <script>
            jQuery(document).ready(function($) {
                var index = <?php echo count($form_mappings); ?>;

                // Add New Form Mapping
                $('.xero-add-card').on('click', function() {
                    var newIndex = index++;
                    // Get the form select HTML from the first card and update its name attribute
                    var formSelect = $('.form-mapping-card:first-child select[name*="[form_id]"]').clone();
                    formSelect.attr('name', 'xero_forms_mapping[' + newIndex + '][form_id]');
                    formSelect.val(''); // Reset selection

                    var cardHtml = `
                                                                    <div class="form-mapping-card" data-index="${newIndex}">
                                                                        <div class="card-header">
                                                                            <h4>Form Mapping ${newIndex + 1}</h4>
                                                                            <button type="button" class="button-link toggle-card">
                                                                                <span class="dashicons dashicons-arrow-down"></span>
                                                                            </button>
                                                                        </div>

                                                                        <fieldset class="form-mapping-fields">
                                                                            <div class="form-section basic-settings">
                                                                                <h5>Basic Settings</h5>
                                                                                <div class="form-row">
                                                                                    <div class="form-group">
                                                                                        <label>Form</label>
                                                                                        ${formSelect[0].outerHTML}
                                                                                        <p class="description">Select the Formidable form you want to map to Xero.</p>
                                                                                    </div>
                                                                                    <div class="form-group">
                                                                                        <label>Endpoint</label>
                                                                                        <input type="text"
                                                                                               name="xero_forms_mapping[${newIndex}][endpoint]"
                                                                                               class="regular-text" />
                                                                                                 <p class="description">Specify the Xero API endpoint that this form will interact with.</p>
                                                                                   </div>
                                                                                </div>
                                                                            </div>

                                                                            <div class="form-section mapping-settings">
                                                                                <h5>Field Mappings</h5>
                                                                                <div class="field-mapping-container">
                                                                                    <div class="field-mapping-table">
                                                                                        <div class="field-mapping-header">
                                                                                            <div class="field-mapping-column">Form Field</div>
                                                                                            <div class="field-mapping-column">Xero Field</div>
                                                                                            <div class="field-mapping-column actions">Actions</div>
                                                                                        </div>
                                                                                        <div class="field-mappings-rows">
                                                                                            <?php
                                                                                            $field_mappings = [];
                                                                                            if (!empty($mapping['field_mappings'])) {
                                                                                                $field_mappings = json_decode($mapping['field_mappings'], true);
                                                                                            }

                                                                                            if ($field_mappings) {
                                                                                                foreach ($field_mappings as $xero_field => $form_field) {
                                                                                            ?>
                                                                                                    <div class="field-mapping-row">
                                                                                                        <div class="field-mapping-column">
                                                                                                            <select class="form-field-select" data-form-id="${newIndex}">
                                                                                                                <option value="">Select Form Field</option>
                                                                                                                <?php
                                                                                                                // Fetch fields for the selected form
                                                                                                                $form_fields = FrmField::get_all_for_form($mapping['form_id']);
                                                                                                                foreach ($form_fields as $field) {
                                                                                                                    $selected = ($form_field == $field->id) ? 'selected' : '';
                                                                                                                    echo '<option value="' . esc_attr($field->id) . '" ' . $selected . '>' . esc_html($field->name) . '</option>';
                                                                                                                }
                                                                                                                ?>
                                                                                                            </select>
                                                                                                        </div>
                                                                                                        <div class="field-mapping-column">
                                                                                                            <select class="xero-field-select">
                                                                                                                <option value="">Select Xero Field</option>
                                                                                                                <optgroup label="Basic Info">
                                                                                                                    <option value="ContactID">Contact ID</option>
                                                                                                                    <option value="ContactNumber">Contact Number</option>
                                                                                                                    <option value="AccountNumber">Account Number</option>
                                                                                                                    <option value="Name">Name</option>
                                                                                                                    <option value="FirstName">First Name</option>
                                                                                                                    <option value="LastName">Last Name</option>
                                                                                                                    <option value="FirstName+LastName" <?php if (isset($xero_field)) selected($xero_field, 'FirstName+LastName'); ?>>First Name + Last Name</option>
                                                                                                                    <option value="EmailAddress">Email Address</option>
                                                                                                                    <option value="SkypeUserName">Skype User Name</option>
                                                                                                                    <option value="Website">Website</option>
                                                                                                                    <option value="DefaultCurrency">Default Currency</option>
                                                                                                                    <option value="ContactStatus">Contact Status</option>
                                                                                                                    <option value="IsSupplier">Is Supplier</option>
                                                                                                                    <option value="IsCustomer">Is Customer</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Addresses">
                                                                                                                    <option value="Addresses.AddressType">Address Type</option>
                                                                                                                    <option value="Addresses.AddressLine1">Address Line 1</option>
                                                                                                                    <option value="Addresses.AddressLine2">Address Line 2</option>
                                                                                                                    <option value="Addresses.AddressLine3">Address Line 3</option>
                                                                                                                    <option value="Addresses.AddressLine4">Address Line 4</option>
                                                                                                                    <option value="Addresses.City">City</option>
                                                                                                                    <option value="Addresses.Region">Region</option>
                                                                                                                    <option value="Addresses.PostalCode">Postal Code</option>
                                                                                                                    <option value="Addresses.Country">Country</option>
                                                                                                                    <option value="Addresses.AttentionTo">Attention To</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Phones">
                                                                                                                    <option value="Phones.PhoneType">Phone Type</option>
                                                                                                                    <option value="Phones.PhoneNumber">Phone Number</option>
                                                                                                                    <option value="Phones.PhoneAreaCode">Phone Area Code</option>
                                                                                                                    <option value="Phones.PhoneCountryCode">Phone Country Code</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Contact Persons">
                                                                                                                    <option value="ContactPersons.FirstName">Contact Person First Name</option>
                                                                                                                    <option value="ContactPersons.LastName">Contact Person Last Name</option>
                                                                                                                    <option value="ContactPersons.EmailAddress">Contact Person Email</option>
                                                                                                                    <option value="ContactPersons.IncludeInEmails">Include In Emails</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Financial/Other">
                                                                                                                    <option value="BankAccountDetails">Bank Account Details</option>
                                                                                                                    <option value="TaxNumber">Tax Number</option>
                                                                                                                    <option value="AccountsReceivableTaxType">Accounts Receivable Tax Type</option>
                                                                                                                    <option value="AccountsPayableTaxType">Accounts Payable Tax Type</option>
                                                                                                                    <option value="Discount">Discount</option>
                                                                                                                    <option value="BrandingThemeID">Branding Theme ID</option>
                                                                                                                    <option value="BrandingThemeName">Branding Theme Name</option>
                                                                                                                    <option value="PurchasesDefaultAccountCode">Purchases Default Account Code</option>
                                                                                                                    <option value="SalesDefaultAccountCode">Sales Default Account Code</option>
                                                                                                                    <option value="PaymentTerms.Bills.Day">Payment Terms Bills Day</option>
                                                                                                                    <option value="PaymentTerms.Bills.Type">Payment Terms Bills Type</option>
                                                                                                                    <option value="PaymentTerms.Sales.Day">Payment Terms Sales Day</option>
                                                                                                                    <option value="PaymentTerms.Sales.Type">Payment Terms Sales Type</option>
                                                                                                                    <option value="UpdatedDateUTC">Updated Date UTC</option>
                                                                                                                    <option value="HasAttachments">Has Attachments</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Contact Persons (1-5)">
                                                                                                                    <option value="ContactPerson1.FirstName">Contact Person 1 First Name</option>
                                                                                                                    <option value="ContactPerson1.LastName">Contact Person 1 Last Name</option>
                                                                                                                    <option value="ContactPerson1.EmailAddress">Contact Person 1 Email</option>
                                                                                                                    <option value="ContactPerson1.IncludeInEmails">Contact Person 1 Include In Emails</option>
                                                                                                                    <option value="ContactPerson2.FirstName">Contact Person 2 First Name</option>
                                                                                                                    <option value="ContactPerson2.LastName">Contact Person 2 Last Name</option>
                                                                                                                    <option value="ContactPerson2.EmailAddress">Contact Person 2 Email</option>
                                                                                                                    <option value="ContactPerson2.IncludeInEmails">Contact Person 2 Include In Emails</option>
                                                                                                                    <option value="ContactPerson3.FirstName">Contact Person 3 First Name</option>
                                                                                                                    <option value="ContactPerson3.LastName">Contact Person 3 Last Name</option>
                                                                                                                    <option value="ContactPerson3.EmailAddress">Contact Person 3 Email</option>
                                                                                                                    <option value="ContactPerson3.IncludeInEmails">Contact Person 3 Include In Emails</option>
                                                                                                                    <option value="ContactPerson4.FirstName">Contact Person 4 First Name</option>
                                                                                                                    <option value="ContactPerson4.LastName">Contact Person 4 Last Name</option>
                                                                                                                    <option value="ContactPerson4.EmailAddress">Contact Person 4 Email</option>
                                                                                                                    <option value="ContactPerson4.IncludeInEmails">Contact Person 4 Include In Emails</option>
                                                                                                                    <option value="ContactPerson5.FirstName">Contact Person 5 First Name</option>
                                                                                                                    <option value="ContactPerson5.LastName">Contact Person 5 Last Name</option>
                                                                                                                    <option value="ContactPerson5.EmailAddress">Contact Person 5 Email</option>
                                                                                                                    <option value="ContactPerson5.IncludeInEmails">Contact Person 5 Include In Emails</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Batch Payments">
                                                                                                                    <option value="BatchPayments.BankAccountNumber">Batch Payments Bank Account Number</option>
                                                                                                                    <option value="BatchPayments.BankAccountName">Batch Payments Bank Account Name</option>
                                                                                                                    <option value="BatchPayments.Details">Batch Payments Details</option>
                                                                                                                    <option value="BatchPayments.Code">Batch Payments Code</option>
                                                                                                                    <option value="BatchPayments.Reference">Batch Payments Reference</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Tracking Categories">
                                                                                                                    <option value="SalesTrackingCategory1.Name">Sales Tracking Category 1 Name</option>
                                                                                                                    <option value="SalesTrackingOption1.Name">Sales Tracking Option 1 Name</option>
                                                                                                                    <option value="SalesTrackingCategory2.Name">Sales Tracking Category 2 Name</option>
                                                                                                                    <option value="SalesTrackingOption2.Name">Sales Tracking Option 2 Name</option>
                                                                                                                    <option value="PurchaseTrackingCategory1.Name">Purchase Tracking Category 1 Name</option>
                                                                                                                    <option value="PurchaseTrackingOption1.Name">Purchase Tracking Option 1 Name</option>
                                                                                                                    <option value="PurchaseTrackingCategory2.Name">Purchase Tracking Category 2 Name</option>
                                                                                                                    <option value="PurchaseTrackingOption2.Name">Purchase Tracking Option 2 Name</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Balances">
                                                                                                                    <option value="Balances.AccountsReceivable.Outstanding">Accounts Receivable Outstanding</option>
                                                                                                                    <option value="Balances.AccountsReceivable.Overdue">Accounts Receivable Overdue</option>
                                                                                                                    <option value="Balances.AccountsPayable.Outstanding">Accounts Payable Outstanding</option>
                                                                                                                    <option value="Balances.AccountsPayable.Overdue">Accounts Payable Overdue</option>
                                                                                                                </optgroup>
                                                                                                                <optgroup label="Other">
                                                                                                                    <option value="XeroNetworkKey">Xero Network Key</option>
                                                                                                                    <option value="ContactGroupId">Contact Group ID</option>
                                                                                                                    <option value="BrandingThemeId">Branding Theme ID</option>
                                                                                                                    <option value="BrandingThemeName">Branding Theme Name</option>
                                                                                                                    <option value="TenantId">Tenant ID</option>
                                                                                                                </optgroup>
                                                                                                            </select>
                                                                                                        </div>
                                                                                                        <div class="field-mapping-column actions">
                                                                                                            <button type="button" class="button remove-mapping">
                                                                                                                <span class="dashicons dashicons-trash"></span>
                                                                                                            </button>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <?php
                                                                                                }
                                                                                            }
                                                                                                    ?>
                                                                                        </div>
                                                                                    </div>
                                                                                    <button type="button" class="button add-field-mapping">
                                                                                        <span class="dashicons dashicons-plus-alt"></span> Add Field Mapping
                                                                                    </button>
                                                                                    <input type="hidden" name="xero_forms_mapping[${newIndex}][field_mappings]"
                                                                                        class="field-mappings-json" value="{}" />
                                                                                </div>
                                                                                    <div class="form-row">
                                                                                        <div class="form-group">
                                                                                            <label>Required Fields</label>
                                                                                            <input type="text" name="xero_forms_mapping[${newIndex}][required_fields]"
                                                                                                value="Name,EmailAddress"
                                                                                                class="regular-text" />
                                                                                            <p class="description">List any fields that are required for submission to Xero, separated by commas.</p>
                                                                                        </div>
                                                                                        <div class="form-group">
                                                                                            <label>Unique ID Field</label>
                                                                                            <select name="xero_forms_mapping[${newIndex}][unique_id_field]" class="regular-text">
                                                                                                <option value="">Select Unique ID Field</option>
                                                                                            </select>
                                                                                            <p class="description">Specify the field that contains the unique identifier for each entry.</p>
                                                                                        </div>
                                                                                    </div>
                                                                            </div>

                                                                            <div class="form-section additional-settings">
                                                                                <h5>Additional Settings</h5>
                                                                                <div class="form-row">
                                                                                    <div class="form-group">
                                                                                        <label>Button Text</label>
                                                                                        <input type="text"
                                                                                               name="xero_forms_mapping[${newIndex}][button_text]"
                                                                                               value="Import from Xero"
                                                                                               class="regular-text" />
                                                                                                     <p class="description">Customize the text displayed on the button for importing data from Xero.</p>

                                                                                    </div>
                                                                                    <div class="form-group checkbox-group">
                                                                                        <label>
                                                                                            <input type="checkbox"
                                                                                                   name="xero_forms_mapping[${newIndex}][include_in_fetch]"
                                                                                                   value="1" />
                                                                                            Include in Fetch
                                                                                        </label>
                                                                                              <p class="description">Check this box to include this mapping in the data fetch process from Xero.</p>

                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                            <div class="form-section shortcode-section">
                                                                                <h5>Shortcode</h5>
                                                                                <div class="form-row">
                                                                                    <code class="shortcode-display">[xero_sync_button form_id="${newIndex}" button_text="Import from Xero"]</code>
                                                                                </div>
                                                                            </div>
                                                                        </fieldset>

                                                                        <div class="card-footer">
                                                                            <button type="button" class="button xero-remove-card">
                                                                                <span class="dashicons dashicons-trash"></span> Remove
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                `;
                    $('#form_mappings_container').append(cardHtml);


                    var $newCard = $('#form_mappings_container').children().last();
                    $newCard.hide().slideDown(300);
                });

                // Remove Form Mapping
                $(document).on('click', '.xero-remove-card', function() {
                    var $card = $(this).closest('.form-mapping-card');
                    $card.slideUp(300, function() {
                        $(this).remove();
                    });
                });

                $(document).on('click', '.toggle-card', function() {
                    $(this).closest('.form-mapping-card').find('.form-mapping-fields').slideToggle();
                    $(this).find('.dashicons').toggleClass('dashicons-arrow-down dashicons-arrow-up');
                });

                // Form validation
                $('form').on('submit', function(e) {
                    var isValid = true;
                    $('.form-mapping-card').each(function() {
                        var $card = $(this);
                        var formId = $card.find('select[name*="[form_id]"]').val();
                        var endpoint = $card.find('input[name*="[endpoint]"]').val();

                        if (!formId || !endpoint) {
                            isValid = false;
                            // Show the form section if it's hidden
                            $card.find('.form-mapping-fields').slideDown(300);
                            // Add visual feedback
                            if (!formId) {
                                $card.find('select[name*="[form_id]"]').addClass('error');
                            }
                            if (!endpoint) {
                                $card.find('input[name*="[endpoint]"]').addClass('error');
                            }
                            return false;
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert('Please fill in all required fields (Form and Endpoint are required).');
                    }
                });

                // Add error class handling
                $(document).on('change', 'select[name*="[form_id]"], input[name*="[endpoint]"]', function() {
                    if ($(this).val()) {
                        $(this).removeClass('error');
                    }

                    if ($(this).attr('name').indexOf('[form_id]') !== -1) {
                        var formId = $(this).val();
                        var $card = $(this).closest('.form-mapping-card');
                        var cardIndex = $card.data('index');

                        if (formId) {
                            // Make an AJAX request to get form fields
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'get_form_fields',
                                    form_id: formId,
                                    nonce: '<?php echo wp_create_nonce("get_form_fields_nonce"); ?>'
                                },
                                success: function(response) {
                                    console.log('AJAX Response:', response);
                                    if (response.success && response.data) {
                                        // Update the unique ID field dropdown
                                        var $uniqueIdSelect = $card.find('select[name*="[unique_id_field]"]');
                                        $uniqueIdSelect.find('option:not(:first)').remove();

                                        // Add the form fields as options
                                        $.each(response.data, function(id, name) {
                                            $uniqueIdSelect.append('<option value="' + id + '">' + name + '</option>');
                                        });

                                        // Also update form field selects in field mappings
                                        var $formFieldSelects = $card.find('.form-field-select');
                                        console.log('Found form field selects:', $formFieldSelects.length);
                                        $formFieldSelects.each(function() {
                                            var $select = $(this);
                                            var currentVal = $select.val();
                                            $select.find('option:not(:first)').remove();

                                            $.each(response.data, function(id, name) {
                                                $select.append('<option value="' + id + '">' + name + '</option>');
                                            });

                                            // Restore selected value if possible
                                            if (currentVal) {
                                                $select.val(currentVal);
                                            }
                                        });
                                        console.log('Form fields updated successfully');
                                    } else {
                                        console.error('AJAX response error:', response);
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('AJAX error:', status, error);
                                    console.error('Response:', xhr.responseText);
                                }
                            });
                        } else {
                            // Clear the unique ID field dropdown if no form is selected
                            var $uniqueIdSelect = $card.find('select[name*="[unique_id_field]"]');
                            $uniqueIdSelect.find('option:not(:first)').remove();

                            // Clear form field selects in field mappings
                            var $formFieldSelects = $card.find('.form-field-select');
                            $formFieldSelects.each(function() {
                                $(this).find('option:not(:first)').remove();
                            });
                        }
                    }
                });

                // Add scope functionality
                $('.add-scope').on('click', function() {
                    $('#scopes-container').append('<div class="scope-item"><input type="text" name="xero_scopes[]" class="regular-text" /><button type="button" class="button remove-scope">Remove</button></div>');
                });

                $(document).on('click', '.remove-scope', function() {
                    $(this).closest('.scope-item').remove();
                });

                // Test AJAX call button
                $(document).on('click', '.test-ajax-call', function() {
                    var $card = $(this).closest('.form-mapping-card');
                    var formId = $card.find('select[name*="[form_id]"]').val();

                    if (!formId) {
                        alert('Please select a form first');
                        return;
                    }

                    console.log('Testing AJAX call for form ID:', formId);

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'get_form_fields',
                            form_id: formId,
                            nonce: '<?php echo wp_create_nonce("get_form_fields_nonce"); ?>'
                        },
                        success: function(response) {
                            console.log('Test AJAX Success:', response);
                            alert('AJAX Success! Check console for details.');
                        },
                        error: function(xhr, status, error) {
                            console.error('Test AJAX Error:', status, error);
                            console.error('Response:', xhr.responseText);
                            alert('AJAX Error! Check console for details.');
                        }
                    });
                });

                // Add Field Mapping functionality
                $(document).on('click', '.add-field-mapping', function() {
                    var $container = $(this).closest('.field-mapping-container');
                    var $rows = $container.find('.field-mappings-rows');
                    var formId = $container.closest('.form-mapping-card').data('index');

                    // Create new field mapping row
                    var $newRow = $('<div class="field-mapping-row"></div>');

                    // Form field dropdown
                    var $formFieldCol = $('<div class="field-mapping-column"></div>');
                    var $formFieldSelect = $('<select class="form-field-select"></select>');
                    $formFieldSelect.append('<option value="">Select Form Field</option>');

                    // Get form fields for the selected form
                    var selectedFormId = $container.closest('.form-mapping-card').find('select[name*="[form_id]"]').val();
                    if (selectedFormId) {
                        // Clone options from an existing select if available
                        var $existingSelect = $container.find('.form-field-select').first();
                        if ($existingSelect.length) {
                            $existingSelect.find('option').each(function() {
                                $formFieldSelect.append($(this).clone());
                            });
                        }
                    }

                    $formFieldCol.append($formFieldSelect);
                    $newRow.append($formFieldCol);

                    // Xero field dropdown
                    var $xeroFieldCol = $('<div class="field-mapping-column"></div>');
                    var $xeroFieldSelect = $('<select class="xero-field-select"></select>');
                    $xeroFieldSelect.append('<option value="">Select Xero Field</option>');

                    // Add Xero field options (clone from existing select if available)
                    var $existingXeroSelect = $container.find('.xero-field-select').first();
                    if ($existingXeroSelect.length) {
                        $existingXeroSelect.find('optgroup').each(function() {
                            var $optgroup = $(this).clone();
                            $xeroFieldSelect.append($optgroup);
                        });
                    } else {
                        // Add default Xero field options if no existing select
                        var contactFields = $('<optgroup label="Contact Fields"></optgroup>');
                        contactFields.append('<option value="ContactID">Contact ID</option>');
                        contactFields.append('<option value="Name">Name</option>');
                        contactFields.append('<option value="FirstName">First Name</option>');
                        contactFields.append('<option value="LastName">Last Name</option>');
                        contactFields.append('<option value="FirstName+LastName">First Name + Last Name</option>');
                        contactFields.append('<option value="EmailAddress">Email Address</option>');
                        contactFields.append('<option value="Phones.PhoneNumber">Phone Number</option>');
                        contactFields.append('<option value="Addresses.AddressLine1">Address Line 1</option>');
                        contactFields.append('<option value="Addresses.City">City</option>');
                        contactFields.append('<option value="Addresses.Region">Region/State</option>');
                        contactFields.append('<option value="Addresses.PostalCode">Postal Code</option>');
                        contactFields.append('<option value="Addresses.Country">Country</option>');
                        $xeroFieldSelect.append(contactFields);

                        var orgFields = $('<optgroup label="Organization Fields"></optgroup>');
                        orgFields.append('<option value="CompanyNumber">Company Number</option>');
                        orgFields.append('<option value="TaxNumber">Tax Number</option>');
                        orgFields.append('<option value="AccountNumber">Account Number</option>');
                        $xeroFieldSelect.append(orgFields);

                        var statusFields = $('<optgroup label="Status Fields"></optgroup>');
                        statusFields.append('<option value="ContactStatus">Contact Status</option>');
                        statusFields.append('<option value="IsSupplier">Is Supplier</option>');
                        statusFields.append('<option value="IsCustomer">Is Customer</option>');
                        $xeroFieldSelect.append(statusFields);
                    }

                    $xeroFieldCol.append($xeroFieldSelect);
                    $newRow.append($xeroFieldCol);

                    // Actions column with remove button
                    var $actionsCol = $('<div class="field-mapping-column actions"></div>');
                    var $removeBtn = $('<button type="button" class="button remove-mapping"><span class="dashicons dashicons-trash"></span></button>');
                    $actionsCol.append($removeBtn);
                    $newRow.append($actionsCol);

                    // Add the new row to the container
                    $rows.append($newRow);

                    // Update the hidden field mappings JSON
                    updateFieldMappingsJson($container);
                });

                // Remove field mapping row
                $(document).on('click', '.remove-mapping', function() {
                    var $container = $(this).closest('.field-mapping-container');
                    $(this).closest('.field-mapping-row').remove();

                    // Update the hidden field mappings JSON
                    updateFieldMappingsJson($container);
                });

                // Update field mappings when dropdowns change
                $(document).on('change', '.form-field-select, .xero-field-select', function() {
                    var $container = $(this).closest('.field-mapping-container');
                    updateFieldMappingsJson($container);
                });

                // Also update on input events for better responsiveness
                $(document).on('input', '.form-field-select, .xero-field-select', function() {
                    var $container = $(this).closest('.field-mapping-container');
                    updateFieldMappingsJson($container);
                });

                // Update when options are added/removed
                $(document).on('DOMNodeInserted DOMNodeRemoved', '.field-mapping-row', function() {
                    var $container = $(this).closest('.field-mapping-container');
                    if ($container.length) {
                        setTimeout(function() {
                            updateFieldMappingsJson($container);
                        }, 50);
                    }
                });

                // Function to update the hidden field mappings JSON input
                function updateFieldMappingsJson($container) {
                    var mappings = {};

                    $container.find('.field-mapping-row').each(function() {
                        var $row = $(this);
                        var xeroField = $row.find('.xero-field-select').val();
                        var formField = $row.find('.form-field-select').val();

                        if (xeroField && formField) {
                            mappings[xeroField] = formField;
                        }
                    });

                    // Update the hidden input with the JSON string
                    var jsonString = JSON.stringify(mappings);
                    var $hiddenInput = $container.find('.field-mappings-json');
                    $hiddenInput.val(jsonString);
                    $hiddenInput.attr('value', jsonString); // Also set the attribute

                    // Mark as changed
                    $hiddenInput.data('changed', true);

                    // Debug log to verify the update
                    console.log('Updated field mappings JSON for container:', $container.closest('.form-mapping-card').find('h4').text(), 'JSON:', jsonString);

                    return mappings;
                }

                // Add tab switching functionality
                $('.nav-tab-wrapper .nav-tab').on('click', function(e) {
                    e.preventDefault();
                    var targetId = $(this).attr('href').substring(1);

                    // Update active tab
                    $('.nav-tab-wrapper .nav-tab').removeClass('nav-tab-active');
                    $(this).addClass('nav-tab-active');

                    // Show target content
                    $('.tab-content').hide();
                    $('#' + targetId).show();
                });

                // Initialize existing field mappings on page load (after function is defined)
                $('.field-mapping-container').each(function() {
                    var $container = $(this);
                    var $card = $container.closest('.form-mapping-card');
                    var formId = $card.find('select[name*="[form_id]"]').val();

                    // If a form is already selected, trigger the change event to load fields
                    if (formId) {
                        console.log('Form already selected on page load:', formId);
                        $card.find('select[name*="[form_id]"]').trigger('change');
                    }

                    // Set selected values for existing mappings
                    $container.find('.field-mapping-row').each(function() {
                        var $row = $(this);
                        var $xeroSelect = $row.find('.xero-field-select');
                        var $formSelect = $row.find('.form-field-select');

                        // Trigger change events to ensure JSON is updated
                        setTimeout(function() {
                            if ($xeroSelect.val() || $formSelect.val()) {
                                updateFieldMappingsJson($container);
                            }
                        }, 100);
                    });
                });

                // Show initial tab
                var hash = window.location.hash || '#general';
                $('.nav-tab-wrapper .nav-tab[href="' + hash + '"]').click();

                // Ensure field mappings JSON is updated before form submission
                $('form').on('submit', function(e) {
                    console.log('Form submission triggered, updating field mappings...');

                    // Update all field mappings JSON before submitting
                    $('.field-mapping-container').each(function() {
                        updateFieldMappingsJson($(this));
                    });

                    // Force a brief delay to ensure DOM updates are processed
                    if (!$(this).data('mappings-updated')) {
                        e.preventDefault();
                        $(this).data('mappings-updated', true);
                        var $form = $(this);
                        setTimeout(function() {
                            console.log('Resubmitting form after JSON update...');
                            $form.off('submit').submit();
                        }, 200);
                        return false;
                    }
                });
            });
        </script>

        <!-- Add this JavaScript to the existing script section -->
        <script>
            (function($) {
                    if (typeof $ === 'undefined') {
                        console.error('jQuery is not loaded');
                        return;
                    }

                    $(function() {
                            try {
                                // Modal handling
                                const modal = $('#xero-import-modal');
                                const closeBtn = $('.xero-modal-close');

                                closeBtn.on('click', function() {
                                    if (!modal.hasClass('import-in-progress')) {
                                        modal.hide();
                                    }
                                });

                                $(window).on('click', function(e) {
                                    if ($(e.target).is(modal) && !modal.hasClass('import-in-progress')) {
                                        modal.hide();
                                    }
                                });

                                // Function to update modal progress
                                function updateModalProgress(progress, processed, total, rateLimit, recordsSynced, currentBatch) {
                                    $('.xero-modal .progress-bar-fill').css('width', progress + '%');
                                    $('.xero-modal .progress-text').text(
                                        'Processing ' + ' records (' + progress + '%)'
                                    );

                                    // Update stats
                                    if (recordsSynced !== undefined) {
                                        $('.records-synced').text(recordsSynced);
                                    }
                                    if (currentBatch !== undefined) {
                                        $('.current-batch').text(currentBatch);
                                    }

                                    // Update rate limit status
                                    if (rateLimit && rateLimit.is_rate_limited) {
                                        $('.rate-limit-status').html('<span class="rate-limited">Rate limited</span> - ' + rateLimit.rate_limit_problem + ' limit reached');
                                        $('#rate-limit-info').show();
                                        $('.rate-limit-message').text(
                                            "You've hit Xero's " + rateLimit.rate_limit_problem + " API rate limit. Please wait before making more requests."
                                        );
                                        $('.import-status').text('Rate limited - waiting to retry');
                                        startRateLimitTimer(rateLimit.wait_time);
                                    } else {
                                        $('.rate-limit-status').html('<span class="rate-normal">Normal</span> - No rate limits reached');
                                        $('#rate-limit-info').hide();
                                        if (progress < 100) {
                                            $('.import-status').text('Importing data...');
                                        } else {
                                            $('.import-status').text('Import complete!');
                                        }
                                    }
                                }

                                // Function to check progress and update modal
                                function checkImportProgress(formId) {
                                    // First check rate limit status
                                    $.ajax({
                                            url: ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'check_xero_rate_limit',
                                                nonce: '<?php echo wp_create_nonce("check_xero_rate_limit_nonce"); ?>'
                                            },
                                            success: function(rateLimitResponse) {
                                                // Check if we're rate limited
                                                if (rateLimitResponse.success && rateLimitResponse.data && rateLimitResponse.data.is_rate_limited) {
                                                    const rateLimit = rateLimitResponse.data;

                                                    // Update UI to show rate limit
                                                    $('.rate-limit-status').html('<span class="rate-limited">Rate limited</span> - ' + rateLimit.rate_limit_problem + ' limit reached');
                                                    $('#rate-limit-info').show();
                                                    $('.rate-limit-message').text(
                                                        "You've hit Xero's " + rateLimit.rate_limit_problem + " API rate limit. Please wait before making more requests."
                                                    );
                                                    $('.import-status').text('Rate limited - waiting to retry');
                                                    startRateLimitTimer(rateLimit.wait_time);

                                                    // Check progress again after a delay
                                                    setTimeout(function() {
                                                        checkImportProgress(formId);
                                                    }, 5000);

                                                    return;
                                                }

                                                // If not rate limited, proceed with checking progress
                                                $.ajax({
                                                        url: ajaxurl,
                                                        type: 'POST',
                                                        data: {
                                                            action: 'check_xero_import_progress',
                                                            form_id: formId,
                                                            nonce: '<?php echo wp_create_nonce("check_xero_import_progress_nonce"); ?>'
                                                        },
                                                        success: function(response) {
                                                            if (response.success && response.data) {
                                                                const progress = response.data.progress;
                                                                const processed = response.data.processed;
                                                                const total = response.data.total;
                                                                const rateLimit = response.data.rate_limit;
                                                                const recordsSynced = response.data.records_synced;
                                                                const currentBatch = response.data.current_batch;

                                                                updateModalProgress(progress, processed, total, rateLimit, recordsSynced, currentBatch);

                                                                if (progress < 100) {
                                                                    // Automatically continue the import
                                                                    $.ajax({
                                                                        url: window.location.href,
                                                                        type: 'POST',
                                                                        data: {
                                                                            continue_import: formId
                                                                        },
                                                                        success: function() {
                                                                            // Check progress again after a delay
                                                                            setTimeout(function() {
                                                                                checkImportProgress(formId);
                                                                            }, 2000);
                                                                        },
                                                                        error: function(xhr, status, error) {
                                                                            // Check if this is a rate limit error (status code 429)
                                                                            if (xhr.status === 429) {
                                                                                // Extract rate limit information from headers if available
                                                                                const retryAfter = xhr.getResponseHeader('retry-after');
                                                                                const rateLimitProblem = xhr.getResponseHeader('x-rate-limit-problem');

                                                                                if (retryAfter) {
                                                                                    const waitTime = parseInt(retryAfter);
                                                                                    const problem = rateLimitProblem || 'unknown';

                                                                                    // Show rate limit information
                                                                                    $('#rate-limit-info').show();
                                                                                    $('.rate-limit-message').text(
                                                                                        "You've hit Xero's " + problem + " API rate limit. Please wait before making more requests."
                                                                                    );
                                                                                    $('.import-status').text('Rate limited - waiting to retry');
                                                                                    startRateLimitTimer(waitTime);
                                                                                }
                                                                            }

                                                                            $('#modal-message').html(
                                                                                '<div class="alert alert-error">Error continuing import: ' + error + '</div>'
                                                                            );
                                                                        }
                                                                    });
                                                                } else {
                                                                    // Update UI for completed import
                                                                    $('.xero-modal-header').addClass('import-complete');
                                                                    $('.xero-modal-header h3').text('Import Progress Complete');
                                                                    $('.progress-bar-fill').addClass('complete');
                                                                    setTimeout(function() {
                                                                        modal.hide();
                                                                        location.reload();
                                                                    }, 2000);
                                                                }
                                                            }
                                                        },
                                                        error: function() {
                                                            $('#modal-message').html(
                                                                '<div class="alert alert-error">Failed to check import progress</div>'
                                                            );
                                                        }
                                                    },
                                                    error: function() {
                                                        // If rate limit check fails, proceed with checking progress directly
                                                        $.ajax({
                                                            url: ajaxurl,
                                                            type: 'POST',
                                                            data: {
                                                                action: 'check_xero_import_progress',
                                                                form_id: formId,
                                                                nonce: '<?php echo wp_create_nonce("check_xero_import_progress_nonce"); ?>'
                                                            },
                                                            success: function(response) {
                                                                // Handle response
                                                                if (response.success && response.data) {
                                                                    updateModalProgress(response.data.progress, response.data.processed, response.data.total, response.data.rate_limit, response.data.records_synced, response.data.current_batch);
                                                                }
                                                            }
                                                        });
                                                    }
                                                });
                                        }

                                        // Add cancel button functionality
                                        $(document).on('click', '.cancel-import-button', function() {
                                            // Show cancel confirmation dialog
                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'show_cancel_confirmation',
                                                    nonce: '<?php echo wp_create_nonce("show_cancel_confirmation_nonce"); ?>'
                                                },
                                                success: function(response) {
                                                    if (response.success && response.data) {
                                                        // Update modal content with confirmation dialog
                                                        $('.xero-modal-header h3').text('Cancel Import');
                                                        $('.xero-modal-body').html(response.data.html);

                                                        // Handle confirm cancel button click
                                                        $('.confirm-cancel').on('click', function() {
                                                            // Send cancel request
                                                            $.ajax({
                                                                url: ajaxurl,
                                                                type: 'POST',
                                                                data: {
                                                                    action: 'cancel_xero_import',
                                                                    nonce: '<?php echo wp_create_nonce("cancel_xero_import_nonce"); ?>'
                                                                },
                                                                success: function(response) {
                                                                    if (response.success) {
                                                                        // Show success message
                                                                        $('.xero-modal-body').html('<div class="alert alert-success">Import canceled successfully.</div>');
                                                                        setTimeout(function() {
                                                                            modal.hide();
                                                                            location.reload();
                                                                        }, 2000);
                                                                    } else {
                                                                        // Show error message
                                                                        $('.xero-modal-body').html('<div class="alert alert-error">Failed to cancel import: ' + response.data + '</div>');
                                                                    }
                                                                },
                                                                error: function() {
                                                                    $('.xero-modal-body').html('<div class="alert alert-error">Failed to cancel import. Please try again.</div>');
                                                                }
                                                            });
                                                        });

                                                        // Handle continue import button click
                                                        $('.continue-import').on('click', function() {
                                                            // Restore the import progress UI
                                                            location.reload();
                                                        });
                                                    }
                                                },
                                                error: function() {
                                                    alert('Failed to show cancel confirmation. Please try again.');
                                                }
                                            });
                                        });

                                        $('.xero-sync-form').on('submit', function(e) {
                                            if (e.originalEvent && e.originalEvent.submitter && e.originalEvent.submitter.name === 'cancel_import') {
                                                return true;
                                            }

                                            e.preventDefault();

                                            // Show confirmation dialog in the modal
                                            modal.show().removeClass('import-in-progress');
                                            $('.xero-modal-header h3').text('Confirm Sync');
                                            $('.xero-modal-content').html(
                                                '<div class="confirmation-message">Are you sure you want to start the sync process? This may take a while depending on the amount of records.</div>' +
                                                '<div class="modal-buttons">' +
                                                '<button type="button" class="button button-primary confirm-sync">Yes, Start Sync</button> ' +
                                                '<button type="button" class="button cancel-sync">Cancel</button>' +
                                                '</div>'
                                            );

                                            // Store form data for later use
                                            var syncFormId = formId;

                                            // Handle confirmation button click
                                            $('.confirm-sync').on('click', function() {
                                                startSync(syncFormId);
                                            });

                                            // Handle cancel button click
                                            $('.cancel-sync').on('click', function() {
                                                modal.hide();
                                            });

                                            return;
                                        });

                                        // Function to start the sync process
                                        function startSync(formId) {

                                            // Show modal and reset progress
                                            modal.show().addClass('import-in-progress');
                                            $('.xero-modal .progress-bar-fill').css('width', '0%');
                                            $('.xero-modal .progress-text').text('Starting import...');
                                            $('.import-status').text('Initializing...');
                                            $('.records-synced').text('0');
                                            $('.current-batch').text('0');
                                            $('.rate-limit-status').text('Checking rate limit status...');
                                            $('#rate-limit-info').hide();
                                            $('#modal-message').empty();

                                            // First check if we're rate limited
                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'check_xero_rate_limit',
                                                    nonce: '<?php echo wp_create_nonce("check_xero_rate_limit_nonce"); ?>'
                                                },
                                                success: function(response) {
                                                    if (response.success && response.data) {
                                                        const rateLimit = response.data;

                                                        // Update rate limit status in the modal
                                                        if (rateLimit.is_rate_limited) {
                                                            $('.rate-limit-status').html('<span class="rate-limited">Rate limited</span> - ' + rateLimit.rate_limit_problem + ' limit reached');
                                                            $('#rate-limit-info').show();
                                                            $('.rate-limit-message').text(
                                                                "You've hit Xero's " + rateLimit.rate_limit_problem + " API rate limit. Please wait before making more requests."
                                                            );
                                                            $('.import-status').text('Rate limited - waiting to retry');
                                                            startRateLimitTimer(rateLimit.wait_time);

                                                            // Show warning message
                                                            $('#modal-message').html(
                                                                '<div class="alert alert-warning">Import cannot start due to rate limit. Please wait for the timer to complete.</div>'
                                                            );
                                                            return;
                                                        } else {
                                                            $('.rate-limit-status').html('<span class="rate-normal">Normal</span> - No rate limits reached');
                                                        }
                                                    }

                                                    // If we're not rate limited or check failed, proceed with the import
                                                    proceedWithImport(formId);
                                                },
                                                error: function() {
                                                    // If check fails, assume we're not rate limited and proceed
                                                    $('.rate-limit-status').html('<span class="rate-normal">Normal</span> - No rate limits reached');
                                                    proceedWithImport(formId);
                                                }
                                            });
                                        }

                                        // Function to proceed with the import after rate limit check
                                        function proceedWithImport(formId) {
                                            // Submit the form via AJAX
                                            $.ajax({
                                                url: window.location.href,
                                                type: 'POST',
                                                data: {
                                                    sync_form: formId
                                                },
                                                success: function() {
                                                    // Start checking progress after a short delay
                                                    setTimeout(function() {
                                                        checkImportProgress(formId);
                                                    }, 2000);
                                                },
                                                error: function() {
                                                    modal.removeClass('import-in-progress');
                                                    $('#modal-message').html(
                                                        '<div class="alert alert-error">Failed to start import</div>'
                                                    );
                                                }
                                            });
                                        });
                                } catch (error) {
                                    console.error('Error in Xero sync button handler:', error);
                                }
                            });
                    })(jQuery);
        </script>
    <?php
    }

    public function get_auth_url()
    {
        $scopes = get_option('xero_scopes', []);
        if (!is_array($scopes)) {
            $scopes = []; // Ensure it's an array
        }
        $scope_string = implode(' ', $scopes); // Convert array to space-separated string

        $params = [
            'response_type' => 'code',
            'client_id' => $this->client_id,
            'redirect_uri' => $this->redirect_uri,
            'scope' => $scope_string,
            'state' => wp_create_nonce('xero_oauth_state')
        ];

        return 'https://login.xero.com/identity/connect/authorize?' . http_build_query($params);
    }

    public function auth_button()
    {
        $auth_url = $this->get_auth_url();

        return '<a href="' . esc_url($auth_url) . '" class="ui-button">
                    <img width="32" height="32" src="' . esc_url(plugin_dir_url(__FILE__) . 'assets/images/xero-logo.png') . '" alt="" style="vertical-align: middle;"/> Connect to Xero
                </a>';
    }

    public function handle_oauth_redirect()
    {
        if (isset($_GET['code']) && isset($_GET['state'])) {
            if (!wp_verify_nonce($_GET['state'], 'xero_oauth_state')) {
                wp_die('Invalid state');
            }
            $code = sanitize_text_field($_GET['code']);
            $response = wp_remote_post('https://identity.xero.com/connect/token', [
                'body' => [
                    'grant_type' => 'authorization_code',
                    'code' => $code,
                    'redirect_uri' => $this->redirect_uri,
                    'client_id' => $this->client_id,
                    'client_secret' => $this->client_secret
                ]
            ]);
            if (is_wp_error($response)) {
                $this->log_message('OAuth error: ' . $response->get_error_message(), 'error');
                wp_die('An error occurred: ' . $response->get_error_message());
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            if (isset($data['access_token'])) {
                update_option('xero_access_token', $data['access_token']);
                update_option('xero_refresh_token', $data['refresh_token']);
                update_option('xero_token_expires', time() + $data['expires_in']);
                wp_redirect($this->redirect_uri);
                exit;
            } else {
                wp_die('An error occurred: ' . esc_html($body));
            }
        }
    }

    private function get_tenant_id($access_token)
    {
        $this->log_message('Attempting to retrieve tenant ID', 'info');

        // First check if we have a cached tenant ID
        $cached_tenant_id = get_transient('xero_tenant_id');
        if ($cached_tenant_id) {
            $this->log_message('Using cached tenant ID: ' . $cached_tenant_id, 'info');
            return $cached_tenant_id;
        }

        if (empty($access_token)) {
            $this->log_message('Access token is empty', 'error');
            return false;
        }

        // Set a longer timeout for the API request
        $args = [
            'timeout' => 15, // Increase timeout to 15 seconds
            'headers' => [
                'Authorization' => 'Bearer ' . $access_token,
                'Accept' => 'application/json'
            ]
        ];

        $this->log_message('Making request to Xero connections API', 'debug');

        // Make the API request with error handling
        try {
            $response = wp_remote_get('https://api.xero.com/connections', $args);

            if (is_wp_error($response)) {
                $this->log_message('Error getting tenant ID: ' . $response->get_error_message(), 'error');

                // If we have a cached tenant ID even though it's expired, use it as fallback
                $fallback_tenant_id = get_option('xero_last_tenant_id');
                if ($fallback_tenant_id) {
                    $this->log_message('Using fallback tenant ID due to API error', 'warning');
                    return $fallback_tenant_id;
                }

                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            $this->log_message("Tenant ID response code: $response_code", 'debug');
            $this->log_message("Tenant ID response body: $body", 'debug');

            if ($response_code !== 200) {
                $this->log_message("Invalid response code getting tenant ID: $response_code", 'error');
                if ($response_code === 401) {
                    $this->log_message('Unauthorized response, attempting to refresh token', 'info');
                    if ($this->refresh_access_token()) {
                        $new_token = get_option('xero_access_token');
                        return $this->get_tenant_id($new_token);
                    }
                }

                // Use fallback tenant ID if available
                $fallback_tenant_id = get_option('xero_last_tenant_id');
                if ($fallback_tenant_id) {
                    $this->log_message('Using fallback tenant ID due to invalid response', 'warning');
                    return $fallback_tenant_id;
                }

                return false;
            }

            $data = json_decode($body, true);

            if (empty($data)) {
                $this->log_message('No connections found in Xero response', 'error');
                return false;
            }

            if (!isset($data[0]['tenantId'])) {
                $this->log_message('No tenant ID found in response: ' . print_r($data, true), 'error');
                return false;
            }

            $tenant_id = $data[0]['tenantId'];
            $this->log_message('Successfully retrieved tenant ID: ' . $tenant_id, 'success');

            // Cache the tenant ID for 1 hour
            set_transient('xero_tenant_id', $tenant_id, HOUR_IN_SECONDS);

            // Also store as a fallback option
            update_option('xero_last_tenant_id', $tenant_id);

            return $tenant_id;
        } catch (Exception $e) {
            $this->log_message('Exception while getting tenant ID: ' . $e->getMessage(), 'error');

            // Use fallback tenant ID if available
            $fallback_tenant_id = get_option('xero_last_tenant_id');
            if ($fallback_tenant_id) {
                $this->log_message('Using fallback tenant ID due to exception', 'warning');
                return $fallback_tenant_id;
            }

            return false;
        }
    }

    private function refresh_access_token()
    {
        $refresh_token = get_option('xero_refresh_token');
        $this->log_message('Attempting to refresh access token with refresh token: ' . $refresh_token, 'info');

        $response = wp_remote_post('https://identity.xero.com/connect/token', [
            'body' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => $refresh_token,
                'client_id' => $this->client_id,
                'client_secret' => $this->client_secret
            ]
        ]);

        if (is_wp_error($response)) {
            $this->notify_admin('Failed to refresh access token: ' . $response->get_error_message());
            $this->log_message('Failed to refresh access token: ' . $response->get_error_message(), 'error');
            return false;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['access_token'])) {
            update_option('xero_access_token', $data['access_token']);
            update_option('xero_refresh_token', $data['refresh_token']);
            update_option('xero_token_expires', time() + $data['expires_in']);
            $this->log_message('Access token refreshed successfully.', 'success');
            return true;
        }

        $this->notify_admin('Failed to refresh access token: Invalid response from Xero.');
        $this->log_message('Failed to refresh access token: Invalid response from Xero.', 'error');
        return false;
    }

    private function refresh_access_token_if_needed()
    {
        $token_expires = get_option('xero_token_expires', 0);
        $buffer_time = 300; // 5 minutes buffer

        if (time() + $buffer_time >= $token_expires) {
            $this->log_message('Access token expired or about to expire, refreshing...', 'info');
            return $this->refresh_access_token();
        }

        return true;
    }

    /**
     * Format a time duration in seconds to a human-readable string
     *
     * @param int $seconds Time in seconds
     * @return string Formatted time string
     */
    private function format_time_duration($seconds)
    {
        if ($seconds < 60) {
            return "$seconds seconds";
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remaining_seconds = $seconds % 60;
            return "$minutes minute" . ($minutes > 1 ? 's' : '') .
                ($remaining_seconds > 0 ? " and $remaining_seconds second" . ($remaining_seconds > 1 ? 's' : '') : '');
        } elseif ($seconds < 86400) {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return "$hours hour" . ($hours > 1 ? 's' : '') .
                ($minutes > 0 ? " and $minutes minute" . ($minutes > 1 ? 's' : '') : '');
        } else {
            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            return "$days day" . ($days > 1 ? 's' : '') .
                ($hours > 0 ? " and $hours hour" . ($hours > 1 ? 's' : '') : '');
        }
    }



    /**
     * Check if we're currently rate limited by Xero API
     *
     * @return bool|array False if not rate limited, array with rate limit info if rate limited
     */
    private function is_rate_limited()
    {
        // Get rate limit information
        $rate_limit_hit = get_option('xero_rate_limit_hit', 0);
        $retry_after = get_option('xero_rate_limit_retry_after', 0);
        $rate_limit_problem = get_option('xero_rate_limit_problem', '');

        // If we have a rate limit hit timestamp
        if ($rate_limit_hit > 0) {
            $current_time = time();
            $time_passed = $current_time - $rate_limit_hit;

            // If we're still within the retry-after period
            if ($time_passed < $retry_after) {
                $wait_time = $retry_after - $time_passed;
                return [
                    'rate_limited' => true,
                    'retry_after' => $retry_after,
                    'wait_time' => $wait_time,
                    'rate_limit_problem' => $rate_limit_problem
                ];
            } else {
                // We've waited long enough, clear the rate limit flags
                if (function_exists('delete_option')) {
                    delete_option('xero_rate_limit_hit');
                    delete_option('xero_rate_limit_retry_after');
                    delete_option('xero_rate_limit_problem');
                }
            }
        }

        return false;
    }

    /**
     * Fetch data from Xero API with support for pagination and large datasets
     *
     * @param string $endpoint The API endpoint to fetch data from
     * @param int $page The page number to fetch (default: 1)
     * @param int $page_size The number of records per page (default: 200)
     * @param array $all_data Accumulated data for pagination (used internally)
     * @param int $max_records Maximum number of records to fetch (0 = no limit)
     * @return array|WP_Error The fetched data or error
     */
    public function fetch_xero_data($endpoint, $page_size, $page = 1, $all_data = [], $max_records = 0)
    {
        // Extract the resource type from the endpoint URL for later use
        $url_parts = parse_url($endpoint);
        $path_segments = explode('/', trim($url_parts['path'] ?? '', '/'));
        $resource_type = end($path_segments);

        // Check if we're rate limited before making any requests
        $rate_limit_check = $this->is_rate_limited();
        if ($rate_limit_check !== false) {
            // Normal rate limit handling
            $wait_time = $rate_limit_check['wait_time'];
            $problem = $rate_limit_check['rate_limit_problem'];
            $this->log_message("Still rate limited ($problem). Need to wait $wait_time more seconds before making requests.", 'warning');
            return new WP_Error('rate_limit', "Xero API rate limit reached ($problem). Please try again in $wait_time seconds.", $rate_limit_check);
        }

        if (!$this->refresh_access_token_if_needed()) {
            return new WP_Error('token_refresh_failed', 'Failed to refresh the access token');
        }

        $access_token = get_option('xero_access_token');
        $tenant_id = $this->get_tenant_id($access_token);

        if (!$tenant_id) {
            return new WP_Error('no_tenant_id', 'Could not retrieve tenant ID. Please reconnect to Xero and try again');
        }

        $base_url = get_option('xero_api_base_url');

        // Endpoint should just be the resource name, prepend base URL
        if (strpos($endpoint, 'https://') !== 0) {
            // Remove any leading/trailing slashes from both base_url and endpoint
            $endpoint = rtrim($base_url, '/') . '/' . trim($endpoint, '/');
        }

        // Extract the resource type from the endpoint URL for later use
        $url_parts = parse_url($endpoint);
        $path_segments = explode('/', trim($url_parts['path'] ?? '', '/'));
        $resource_type = end($path_segments);

        // Store the original endpoint without pagination for recursive calls
        $original_endpoint = $endpoint;

        // Add pagination parameters
        $paginated_endpoint = add_query_arg([
            'page' => $page,
            'pageSize' => $page_size
        ], $endpoint);

        $this->log_message("Fetching page $page with page size $page_size from endpoint: $paginated_endpoint", 'info');

        // Increase timeout for large data requests
        // Get timeout setting from admin options, default to 60 seconds
        $timeout = intval(get_option('xero_timeout', 60));

        $response = wp_remote_get($paginated_endpoint, [
            'timeout' => $timeout,
            'headers' => [
                'Authorization' => 'Bearer ' . $access_token,
                'Xero-tenant-id' => $tenant_id,
                'Accept' => 'application/json'
            ]
        ]);

        $this->log_message("API request sent with timeout: $timeout seconds", 'debug');

        if (is_wp_error($response)) {
            $this->log_message("Error in API request: " . $response->get_error_message(), 'error');
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        $response_code = wp_remote_retrieve_response_code($response);
        $headers = wp_remote_retrieve_headers($response);

        // Log the raw response for debugging
        $this->log_message("Response code: $response_code", 'debug');
        $this->log_message("Response headers: " . print_r($headers, true), 'debug');
        $this->log_message("Response body sample: " . substr($body, 0, 500) . (strlen($body) > 500 ? '...' : ''), 'debug');

        if ($response_code < 200 || $response_code >= 300) {
            $error_message = wp_remote_retrieve_response_message($response);
            $this->log_message("Error response from Xero API: $response_code - $error_message", 'error');

            // Handle rate limiting specifically
            if ($response_code === 429) {
                $retry_after = isset($headers['retry-after']) ? intval($headers['retry-after']) : 60;
                $rate_limit_problem = isset($headers['x-rate-limit-problem']) ? $headers['x-rate-limit-problem'] : 'unknown';
                $day_limit_remaining = isset($headers['x-daylimit-remaining']) ? intval($headers['x-daylimit-remaining']) : null;
                $min_limit_remaining = isset($headers['x-minlimit-remaining']) ? intval($headers['x-minlimit-remaining']) : null;

                // Log the rate limit hit
                $this->log_message("Rate limit hit: $rate_limit_problem limit. Retry after $retry_after seconds. Day limit remaining: $day_limit_remaining, Minute limit remaining: $min_limit_remaining", 'warning');



                // If testing mode is not enabled, store the rate limit info and return an error
                if (function_exists('update_option')) {
                    update_option('xero_rate_limit_hit', time());
                    update_option('xero_rate_limit_retry_after', $retry_after);
                    update_option('xero_rate_limit_problem', $rate_limit_problem);
                }

                return new WP_Error('rate_limit', "Xero API rate limit reached ($rate_limit_problem). Please try again in $retry_after seconds.", [
                    'wait_time' => $retry_after,
                    'rate_limit_problem' => $rate_limit_problem
                ]);
            }

            return new WP_Error('api_error', "Error response from Xero API: $response_code - $error_message");
        }

        // Check if we have data in the response
        if (!empty($data) && is_array($data)) {
            $current_page_data = [];

            // First try to get data using the resource type as key
            if (isset($data[$resource_type])) {
                $current_page_data = $data[$resource_type];
            } else {
                // If that fails, look for any array in the response that contains records
                foreach ($data as $key => $value) {
                    if (is_array($value) && !empty($value)) {
                        $current_page_data = $value;
                        break;
                    }
                }

                // If no arrays found but we have data, use the whole response
                if (empty($current_page_data)) {
                    $current_page_data = $data;
                }
            }

            // Merge with existing data if we're paginating
            $all_data = array_merge($all_data, $current_page_data);

            // Check if there are more pages
            $has_more_pages = false;
            $total_records = 0;

            // Look for pagination info in the response
            if (isset($data['pagination'])) {
                $pagination = $data['pagination'];
                $current_page = isset($pagination['page']) ? intval($pagination['page']) : $page;
                $page_count = isset($pagination['pageCount']) ? intval($pagination['pageCount']) : 1;
                $total_records = isset($pagination['itemCount']) ? intval($pagination['itemCount']) : count($current_page_data);

                $this->log_message("Pagination info: Current page $current_page of $page_count total pages, total records: $total_records", 'info');
                $has_more_pages = ($current_page < $page_count);

                // Store the total records count for progress tracking
                update_option('xero_total_records_' . $resource_type, $total_records);
                $this->log_message("Stored total records count for progress tracking: $total_records", 'info');
            } else {
                // Check for Xero-specific pagination headers
                $this->log_message("Checking headers for pagination info: " . print_r($headers, true), 'debug');

                // Some Xero endpoints use Content-Range header: items 1-100/925
                $content_range = isset($headers['content-range']) ? $headers['content-range'] : '';
                if (!empty($content_range) && preg_match('/items (\d+)-(\d+)\/(\d+)/', $content_range, $matches)) {
                    $range_start = intval($matches[1]);
                    $range_end = intval($matches[2]);
                    $total_records = intval($matches[3]);

                    $this->log_message("Found Content-Range header: $content_range, total records: $total_records", 'info');
                    $has_more_pages = ($range_end < $total_records);

                    // Store the total records count for progress tracking
                    update_option('xero_total_records_' . $resource_type, $total_records);
                    $this->log_message("Stored total records count for progress tracking: $total_records", 'info');
                } else {
                    // If no pagination info but we got a full page of results, try next page
                    $has_more_pages = (count($current_page_data) >= $page_size);
                    $this->log_message("No pagination info found. Got " . count($current_page_data) . " records. Assuming " . ($has_more_pages ? "more" : "no more") . " pages.", 'info');

                    // Even without pagination info, store the current count as a minimum
                    // This will be updated if we find more records
                    $current_total = count($all_data);
                    update_option('xero_total_records_' . $resource_type, $current_total);
                    $this->log_message("Stored minimum total records count for progress tracking: $current_total", 'info');

                    // If we don't have pagination info, use the current count as total
                    $total_records = $current_total;
                }
            }

            // Store progress for potential resume
            update_option('xero_import_progress', [
                'endpoint' => $endpoint,
                'current_page' => $page,
                'page_size' => $page_size,
                'total_records' => $total_records,
                'current_count' => count($all_data),
                'timestamp' => time()
            ]);

            // Check if we've reached the maximum number of records to fetch
            if ($max_records > 0 && count($all_data) >= $max_records) {
                $this->log_message("Reached maximum records limit ($max_records). Stopping pagination.", 'info');

                // Store the page we stopped at for later continuation
                update_option('xero_last_page_' . $resource_type, $page);
                update_option('xero_has_more_pages_' . $resource_type, $has_more_pages);

                // Return only up to max_records
                return array_slice($all_data, 0, $max_records);
            }

            // If there are more pages, fetch the next page
            if ($has_more_pages) {
                $this->log_message("Fetching next page of data (page " . ($page + 1) . ")", 'info');

                // Add a small delay to avoid rate limiting
                usleep(500000); // 0.5 second delay

                // Store the current page for potential resume
                update_option('xero_last_page_' . $resource_type, $page);
                update_option('xero_has_more_pages_' . $resource_type, true);

                // Recursive call to get next page
                $next_page_result = $this->fetch_xero_data($original_endpoint, $page_size, $page + 1, $all_data, $max_records);

                // If we got an error on the next page, return what we have so far instead of failing completely
                if (is_wp_error($next_page_result)) {
                    $this->log_message("Error fetching page " . ($page + 1) . ": " . $next_page_result->get_error_message() . ". Returning data collected so far.", 'warning');
                    return $all_data;
                }

                return $next_page_result;
            }

            // Clear progress when complete
            delete_option('xero_import_progress');
            update_option('xero_has_more_pages_' . $resource_type, false);
            $this->log_message("Completed fetching all data. Total records: " . count($all_data), 'info');

            return $all_data;
        }

        return new WP_Error('no_data', 'No data found in Xero response for endpoint: ' . $endpoint);
    }

    /**
     * AJAX handler to get form fields for a specific form
     */
    public function ajax_get_form_fields()
    {
        // Log the request for debugging
        error_log('[XERO DEBUG] AJAX get_form_fields called with data: ' . print_r($_POST, true));

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'get_form_fields_nonce')) {
            error_log('[XERO ERROR] Nonce verification failed');
            wp_die('Security check failed');
        }

        $form_id = intval($_POST['form_id']);

        if (!$form_id) {
            error_log('[XERO ERROR] Invalid form ID: ' . $_POST['form_id']);
            wp_send_json_error('Invalid form ID');
        }

        // Check if Formidable Forms is active
        if (!class_exists('FrmField')) {
            error_log('[XERO ERROR] Formidable Forms is not active');
            wp_send_json_error('Formidable Forms is not active');
        }

        // Get form fields
        $form_fields = FrmField::get_all_for_form($form_id);
        $fields_data = [];

        if (empty($form_fields)) {
            error_log('[XERO WARNING] No fields found for form ID: ' . $form_id);
        }

        foreach ($form_fields as $field) {
            $fields_data[$field->id] = $field->name;
        }

        error_log('[XERO DEBUG] Returning fields data: ' . print_r($fields_data, true));
        wp_send_json_success($fields_data);
    }

    private function map_data_to_form_fields($xero_data, $field_mappings)
    {
        $this->log_message('Mapping fields with data: ' . print_r($xero_data, true), 'info');
        $this->log_message('Using mappings: ' . print_r($field_mappings, true), 'info');

        $entry_data = [];

        foreach ($field_mappings as $xero_field => $form_field) {
            // Check if the xero_field is in the format "Field1+Field2"
            if (strpos($xero_field, '+') !== false) {
                $fields_to_concatenate = explode('+', $xero_field);
                $concatenated_value = '';

                foreach ($fields_to_concatenate as $field) {
                    $value = $this->get_nested_value($xero_data, trim($field));
                    if ($value !== null) {
                        $concatenated_value .= trim($value) . ' ';
                    }
                }

                $entry_data[$form_field] = trim($concatenated_value);
                continue; // Skip to the next mapping
            }

            $value = $this->get_nested_value($xero_data, $xero_field);

            if ($value !== null) {
                $entry_data[$form_field] = $value;
                $this->log_message("Mapped $xero_field to $form_field with value: " . print_r($value, true), 'info');
            } else {
                $this->log_message("Could not find value for Xero field: $xero_field", 'warning');
            }
        }

        return $entry_data;
    }

    private function get_nested_value($array, $path)
    {
        // Handle both dot notation and direct keys
        $keys = strpos($path, '.') !== false ? explode('.', $path) : [$path];
        $value = $array;

        foreach ($keys as $key) {
            if (!is_array($value) || !isset($value[$key])) {
                return null;
            }
            $value = $value[$key];
        }

        return $value;
    }

    /**
     * Sync data from Xero to WordPress with batch processing and progress tracking
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function sync_data($atts)
    {
        $atts = shortcode_atts(['form_id' => '', 'button_text' => 'Import from Xero', 'batch_size' => 0], $atts);
        $form_id = $atts['form_id'];
        $button_text = $atts['button_text'];
        $batch_size = intval($atts['batch_size']);

        // If batch size is not specified in shortcode, use the admin setting
        if ($batch_size <= 0) {
            // Get batch size from admin settings, default to 50 if not set
            $batch_size = intval(get_option('xero_batch_size', 50));

            // Ensure we have a valid batch size
            if ($batch_size <= 0) {
                $batch_size = 50;
            }
        }

        if (!is_user_logged_in()) {
            return 'You must be logged in to use this feature.';
        }

        ob_start();

        // Initialize message variables
        $message = '';
        $message_type = '';

        // Check for existing import progress
        $import_progress = get_option('xero_import_progress_' . $form_id, []);
        $is_import_in_progress = !empty($import_progress) && isset($import_progress['total_records']) && $import_progress['processed'] < $import_progress['total_records'];

        // Check if we're continuing an import
        $continue_import = isset($_POST['continue_import']) && $_POST['continue_import'] == $form_id;

        // Check if we're canceling an import
        if (isset($_POST['cancel_import']) && $_POST['cancel_import'] == $form_id) {
            // Add cancellation message to notifications
            $this->notify_admin('Import was manually canceled by user');

            // Send any collected notifications before clearing the progress
            $this->send_consolidated_notifications();

            delete_option('xero_import_progress_' . $form_id);
            $message = 'Import canceled.';
            $message_type = 'info';
            $is_import_in_progress = false;
        }

        // Check if the sync button was clicked or we're continuing an import
        if ((isset($_POST['sync_form']) && $_POST['sync_form'] == $form_id) || $continue_import) {
            // Set a longer timeout for the import process
            set_time_limit(300); // 5 minutes

            $this->log_message('Starting/continuing Xero sync process for form ID: ' . $form_id, 'info');
            $form_mappings = get_option('xero_forms_mapping', []);
            $mapping = array_filter($form_mappings, function ($m) use ($form_id) {
                return $m['form_id'] == $form_id && isset($m['include_in_fetch']) && $m['include_in_fetch'];
            });

            if ($mapping) {
                $mapping = reset($mapping);
                $field_mappings = json_decode($mapping['field_mappings'], true);
                $endpoint = $mapping['endpoint'];
                $unique_id_field = $mapping['unique_id_field']; // Get the unique ID field

                // Get required fields from mapping
                $required_fields = explode(',', $mapping['required_fields']); // Assuming required fields are comma-separated

                // Initialize or get existing progress data
                if ($continue_import && $is_import_in_progress) {
                    // Continue from where we left off
                    $data = $import_progress['data'];
                    $total_records = $import_progress['total_records'];
                    $processed = $import_progress['processed'];
                    $records_synced = $import_progress['records_synced'];
                    $current_batch = $import_progress['current_batch'];

                    $this->log_message("Continuing import from record $processed of $total_records", 'info');
                } else {
                    // Start a new import
                    $this->log_message("Starting new import from endpoint: $endpoint", 'info');

                    // Get the page size from admin settings for API requests
                    $api_page_size = intval(get_option('xero_api_page_size'));
                    if ($api_page_size <= 0) {
                        $api_page_size = 200; // Default to 200 if not set or invalid
                    }

                    $this->log_message("Using API page size: $api_page_size", 'info');

                    $response = $this->fetch_xero_data($endpoint, $api_page_size, 1);

                    if (is_wp_error($response)) {
                        $error_code = $response->get_error_code();
                        $error_message = $response->get_error_message();

                        if ($error_code === 'rate_limit') {
                            // Get rate limit details
                            $error_data = $response->get_error_data();
                            $wait_time = isset($error_data['wait_time']) ? $error_data['wait_time'] : 0;
                            $rate_limit_problem = isset($error_data['rate_limit_problem']) ? $error_data['rate_limit_problem'] : 'unknown';

                            // Format a more user-friendly message
                            $formatted_wait_time = $this->format_time_duration($wait_time);
                            $message = "<strong>Rate Limit Reached:</strong> You've hit Xero's $rate_limit_problem API rate limit. Please try again in $formatted_wait_time.";
                            $message_type = 'error';
                        } else {
                            $message = 'Error fetching data from ' . esc_html($endpoint) . ': ' . esc_html($error_message);
                            $message_type = 'error';
                        }
                        return ob_get_clean();
                    }

                    // Get the maximum number of records to fetch in one go to avoid memory issues
                    // We'll fetch more as needed during batch processing
                    $max_records_per_fetch = 200;

                    // Fetch the first batch of data with a limit to avoid memory issues
                    $data = $this->fetch_xero_data($endpoint, $api_page_size, 1, [], $max_records_per_fetch);

                    // Check if we have a total records count from the API
                    $resource_type = basename($endpoint);
                    $api_total_records = get_option('xero_total_records_' . $resource_type, 0);

                    // Use the API total if available, otherwise use the count of returned data
                    $total_records = ($api_total_records > 0) ? $api_total_records : count($data);
                    $processed = 0;
                    $records_synced = 0;
                    $current_batch = 0;
                    $current_api_page = 1;
                    $has_more_pages = get_option('xero_has_more_pages_' . $resource_type, false);

                    $this->log_message("Starting new import with $total_records records (API reported: $api_total_records, fetched initially: " . count($data) . ")", 'info');
                }

                // Process the current batch
                $batch_start = $processed;
                $batch_end = min($processed + $batch_size, $total_records);
                $current_batch_items = array_slice($data, $batch_start, $batch_size);
                $batch_item_count = count($current_batch_items);

                $this->log_message("Processing batch $current_batch: records $batch_start to $batch_end of $total_records (batch size: $batch_size, items in this batch: $batch_item_count)", 'info');

                // If we have no items in this batch but still have more records, we need to fetch more data
                if (
                    ($batch_item_count === 0 && $processed < $total_records) ||
                    ($batch_start + $batch_size >= count($data) && $processed < $total_records)
                ) {

                    $this->log_message("Need to fetch more data. Processed $processed of $total_records records so far.", 'info');

                    // Get the resource type from the endpoint
                    $resource_type = basename($endpoint);

                    // Check if there are more pages available from the API
                    $has_more_pages = get_option('xero_has_more_pages_' . $resource_type, false);
                    $last_page = get_option('xero_last_page_' . $resource_type, 1);

                    if ($has_more_pages) {
                        // Get the API page size
                        $api_page_size = intval(get_option('xero_api_page_size'));
                        if ($api_page_size <= 0) {
                            $api_page_size = 200; // Default to 200 if not set or invalid
                        }

                        // Calculate the next page to fetch
                        $next_page = $last_page + 1;
                        $this->log_message("Fetching next page of data (page $next_page) with page size $api_page_size", 'info');

                        // Fetch the next page with a limit to avoid memory issues
                        $max_records_per_fetch = 200;
                        $additional_data = $this->fetch_xero_data($endpoint, $api_page_size, $next_page, [], $max_records_per_fetch);

                        if (is_wp_error($additional_data)) {
                            $error_code = $additional_data->get_error_code();
                            $error_message = $additional_data->get_error_message();

                            if ($error_code === 'rate_limit') {
                                // Get rate limit details
                                $error_data = $additional_data->get_error_data();
                                $wait_time = isset($error_data['wait_time']) ? $error_data['wait_time'] : 0;
                                $rate_limit_problem = isset($error_data['rate_limit_problem']) ? $error_data['rate_limit_problem'] : 'unknown';

                                // Format a more user-friendly message
                                $formatted_wait_time = $this->format_time_duration($wait_time);
                                $this->log_message("Rate limit reached: $rate_limit_problem. Need to wait $formatted_wait_time before continuing.", 'warning');
                            } else {
                                $this->log_message("Error fetching additional data: " . $error_message, 'error');
                            }

                            // Even with an error, we'll continue with what we have
                            $this->log_message("Continuing with existing data despite fetch error", 'info');
                        } elseif (is_array($additional_data) && !empty($additional_data)) {
                            $new_records = count($additional_data);
                            $this->log_message("Successfully fetched $new_records additional records", 'success');

                            // Append the new data to our existing data
                            $data = array_merge($data, $additional_data);

                            // Update the total records count if needed
                            $api_total_records = get_option('xero_total_records_' . $resource_type, 0);
                            if ($api_total_records > $total_records) {
                                $total_records = $api_total_records;
                                $this->log_message("Updated total records count to $total_records based on API response", 'info');
                            }

                            // Recalculate the batch
                            $batch_end = min($processed + $batch_size, $total_records);
                            $current_batch_items = array_slice($data, $batch_start, $batch_size);
                            $batch_item_count = count($current_batch_items);

                            $this->log_message("Updated batch: records $batch_start to $batch_end of $total_records (items in this batch: $batch_item_count)", 'info');
                        } else {
                            $this->log_message("No additional data found. We've reached the end of available records.", 'info');

                            // If we've reached the end but the total records count is higher, adjust it
                            if ($processed + $batch_item_count < $total_records) {
                                $this->log_message("Adjusting total records count from $total_records to " . ($processed + $batch_item_count), 'warning');
                                $total_records = $processed + $batch_item_count;
                            }
                        }
                    } else {
                        $this->log_message("No more pages available from the API. We've reached the end of available records.", 'info');

                        // If we've reached the end but the total records count is higher, adjust it
                        if ($processed + $batch_item_count < $total_records) {
                            $this->log_message("Adjusting total records count from $total_records to " . ($processed + $batch_item_count), 'warning');
                            $total_records = $processed + $batch_item_count;
                        }
                    }
                }

                // Process each item in the current batch
                foreach ($current_batch_items as $item) {
                    $this->log_message('Processing record: ' . json_encode($item), 'debug');
                    $processed++;

                    // Check if required fields are present
                    $missing_required = false;
                    foreach ($required_fields as $required_field) {
                        if (empty($item[trim($required_field)])) {
                            $missing_required = true;
                            $this->log_message('Skipping record due to missing required field: ' . $required_field, 'warning');
                            // Add to notification messages for failed items
                            $this->notify_admin('Failed to import record due to missing required field: ' . $required_field . '. Record data: ' . json_encode($item));
                            break;
                        }
                    }
                    if ($missing_required) {
                        continue;
                    }

                    $entry_data = $this->map_data_to_form_fields($item, $field_mappings);

                    if ($entry_data) {
                        // Get the unique ID value
                        $unique_id_value = null;
                        foreach ($field_mappings as $xero_field => $form_field) {
                            if ($form_field == $unique_id_field) {
                                $unique_id_value = $this->get_nested_value($item, $xero_field);
                                break;
                            }
                        }

                        if ($unique_id_value) {
                            global $wpdb;
                            // Check for existing entry using direct SQL
                            $existing_id = $wpdb->get_var($wpdb->prepare(
                                "SELECT item_id
                                FROM {$wpdb->prefix}frm_item_metas
                                WHERE field_id = %s
                                AND meta_value = %s
                                AND item_id IN (
                                    SELECT id
                                    FROM {$wpdb->prefix}frm_items
                                    WHERE form_id = %d
                                )",
                                $unique_id_field,
                                $unique_id_value,
                                $form_id
                            ));

                            if ($existing_id) {
                                // Update existing entry
                                FrmEntry::update($existing_id, [
                                    'form_id' => $form_id,
                                    'item_meta' => $entry_data
                                ]);
                                $this->log_message('Updated existing entry: ' . $existing_id, 'success');
                            } else {
                                // Create new entry
                                $new_entry_id = FrmEntry::create([
                                    'form_id' => $form_id,
                                    'item_meta' => $entry_data
                                ]);
                                $this->log_message('Created new entry: ' . $new_entry_id, 'success');
                            }
                            $records_synced++;
                        }
                    }

                    // Update progress more frequently (every 5 records) for better real-time updates
                    if ($processed % 5 === 0) {
                        $progress_data = [
                            'form_id' => $form_id,
                            'endpoint' => $endpoint,
                            'data' => $data,
                            'total_records' => $total_records,
                            'processed' => $processed,
                            'records_synced' => $records_synced,
                            'current_batch' => $current_batch,
                            'timestamp' => time()
                        ];
                        update_option('xero_import_progress_' . $form_id, $progress_data);
                    }
                }

                // Update progress after batch completion
                $current_batch++;
                $progress_data = [
                    'form_id' => $form_id,
                    'endpoint' => $endpoint,
                    'data' => $data,
                    'total_records' => $total_records,
                    'processed' => $processed,
                    'records_synced' => $records_synced,
                    'current_batch' => $current_batch,
                    'timestamp' => time()
                ];
                update_option('xero_import_progress_' . $form_id, $progress_data);

                // Check if we've completed all records
                if ($processed >= $total_records) {
                    $message = 'Data sync completed. Records synced: ' . $records_synced;
                    $message_type = 'success';

                    // Send consolidated notifications when import is complete
                    $this->send_consolidated_notifications();

                    // Clean up progress data after notifications are sent
                    delete_option('xero_import_progress_' . $form_id);
                    $is_import_in_progress = false;
                } else {
                    // More batches to process
                    $progress_percent = round(($processed / $total_records) * 100);
                    $message = "Processed $processed of $total_records records ($progress_percent%). Click 'Continue Import' to process the next batch.";
                    $message_type = 'info';
                    $is_import_in_progress = true;
                }
            } else {
                $message = 'No form mapping found for this form ID.';
                $message_type = 'error';
            }
        }
    ?>



        <div class="xero-sync-buttons" style="display: flex; gap: 10px;">
            <form method="post" class="xero-sync-form">
                <?php wp_nonce_field('xero_sync_contacts_nonce', 'xero_sync_contacts_nonce'); ?>

                <button type="submit" name="sync_form" value="<?php echo esc_attr($form_id); ?>"
                    class="xero-sync-button button button-primary" data-form-id="<?php echo esc_attr($form_id); ?>">
                    <span class="button-content">
                        <span class="dashicons dashicons-update" style="margin-right: 5px;"></span>
                        <span class="button-text"><?php echo esc_html($button_text); ?></span>
                    </span>
                </button>

            </form>
        </div>

        <!-- Add Modal Structure -->
        <div id="xero-import-modal" class="xero-modal" style="display: none;">
            <div class="xero-modal-content">
                <div class="xero-modal-header">
                    <h3>Import Progress</h3>
                </div>
                <div class="xero-modal-body">
                    <div class="xero-import-progress" style="margin-bottom: 15px;">
                        <div class="progress-bar">
                            <div class="progress-bar-fill">
                                <div class="progress-bar-pulse"></div>
                            </div>
                        </div>
                        <div class="progress-text" style="margin-top: 5px; font-size: 13px;"></div>
                    </div>
                    <div id="rate-limit-info" style="margin-bottom: 15px; display: none;">
                        <div class="rate-limit-alert"
                            style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">
                            <h4 style="margin-top: 0; margin-bottom: 5px;">Rate Limit Reached</h4>
                            <p class="rate-limit-message" style="margin-bottom: 5px;"></p>
                            <div class="rate-limit-timer" style="font-weight: bold;"></div>
                        </div>
                    </div>
                    <div id="modal-message"></div>
                    <div class="modal-actions">
                        <button type="button" class="button button-secondary xero-cancel-import">Cancel Import</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Include CSS file -->
        <link rel="stylesheet" href="<?php echo plugins_url('assets/css/qx-styles.css', __FILE__); ?>">

        <script>
            (function($) {
                if (typeof $ === 'undefined') {
                    console.error('jQuery is not loaded');
                    return;
                }

                $(function() {
                    try {
                        // Modal handling
                        const modal = $('#xero-import-modal');
                        const progressBar = modal.find('.progress-bar');
                        let isImportCancelled = false;

                        // Function to format time duration
                        function formatTimeDuration(seconds) {
                            if (seconds < 60) {
                                return seconds + " seconds";
                            } else if (seconds < 3600) {
                                var minutes = Math.floor(seconds / 60);
                                var remainingSeconds = seconds % 60;
                                return minutes + " minute" + (minutes > 1 ? "s" : "") +
                                    (remainingSeconds > 0 ? " and " + remainingSeconds + " second" + (remainingSeconds > 1 ? "s" : "") : "");
                            } else if (seconds < 86400) {
                                var hours = Math.floor(seconds / 3600);
                                var minutes = Math.floor((seconds % 3600) / 60);
                                return hours + " hour" + (hours > 1 ? "s" : "") +
                                    (minutes > 0 ? " and " + minutes + " minute" + (minutes > 1 ? "s" : "") : "");
                            } else {
                                var days = Math.floor(seconds / 86400);
                                var hours = Math.floor((seconds % 86400) / 3600);
                                return days + " day" + (days > 1 ? "s" : "") +
                                    (hours > 0 ? " and " + hours + " hour" + (hours > 1 ? "s" : "") : "");
                            }
                        }

                        // Function to update rate limit timer
                        let rateLimitInterval;

                        function startRateLimitTimer(waitTime) {
                            clearInterval(rateLimitInterval);
                            let remainingTime = waitTime;

                            function updateTimer() {
                                if (remainingTime <= 0) {
                                    clearInterval(rateLimitInterval);
                                    $('#rate-limit-info').hide();
                                    $('.import-status').text('Ready to continue...');
                                    return;
                                }

                                $('.rate-limit-timer').text('Retry in: ' + formatTimeDuration(remainingTime));
                                remainingTime--;
                            }

                            updateTimer(); // Run immediately
                            rateLimitInterval = setInterval(updateTimer, 1000);
                        }

                        // Function to update modal progress
                        function updateModalProgress(progress, processed, total, rateLimit, recordsSynced, currentBatch) {
                            $('.xero-modal .progress-bar-fill').css('width', progress + '%');
                            $('.xero-modal .progress-text').text(
                                'Processing ' + ' records (' + progress + '%)'
                            );

                            // Update stats
                            $('.records-synced').text(recordsSynced || 0);
                            $('.current-batch').text(currentBatch || 0);

                            // Check for rate limit
                            if (rateLimit && rateLimit.is_rate_limited) {
                                $('#rate-limit-info').show();
                                $('.rate-limit-message').text(
                                    "You've hit Xero's " + rateLimit.rate_limit_problem + " API rate limit. Please wait before making more requests."
                                );
                                $('.import-status').text('Rate limited - waiting to retry');
                                startRateLimitTimer(rateLimit.wait_time);
                            } else {
                                $('#rate-limit-info').hide();
                                if (progress < 100) {
                                    $('.import-status').text('Importing data...');
                                } else {
                                    $('.import-status').text('Import complete!');
                                }
                            }

                            // Add processing animation when active
                            if (progress < 100) {
                                progressBar.addClass('processing');
                            } else {
                                progressBar.removeClass('processing');
                            }
                        }

                        // Function to show cancel confirmation in modal
                        function showCancelConfirmationModal() {
                            // Save current modal content to restore if user cancels
                            const currentModalContent = $('.xero-modal-body').html();
                            const currentModalHeader = $('.xero-modal-header h3').text();

                            // Configure modal for cancel confirmation
                            $('.xero-modal-header h3').text('Confirm Cancellation');
                            $('.xero-modal-body').html(`
                                <div>
                                    <p style="font-size: 15px; line-height: 1.5; margin: 0; color: #333;">Are you sure you want to cancel the import? This cannot be undone.</p>
                                </div>
                                <div class="modal-actions" style="display: flex; justify-content: flex-end; gap: 12px;">
                                    <button type="button" class="button button-secondary xero-cancel-no">No, Continue Import</button>
                                    <button type="button" class="button button-danger xero-cancel-yes" style="background-color: #dc3545 !important; color: white !important; border-color: #dc3545 !important;">Yes, Cancel Import</button>
                                </div>
                            `);

                            // Handle cancel confirmation buttons
                            $('.xero-cancel-no').on('click', function() {
                                // Restore previous modal content
                                $('.xero-modal-header h3').text(currentModalHeader);
                                $('.xero-modal-body').html(currentModalContent);

                                // Reattach cancel handler
                                $('.xero-cancel-import').on('click', function() {
                                    showCancelConfirmationModal();
                                });
                            });

                            $('.xero-cancel-yes').on('click', function() {
                                isImportCancelled = true;
                                // Show loading state
                                $(this).prop('disabled', true).text('Cancelling...');
                                $('.xero-cancel-no').prop('disabled', true);

                                $.ajax({
                                    url: ajaxurl,
                                    type: 'POST',
                                    data: {
                                        action: 'cancel_xero_import',
                                        nonce: '<?php echo wp_create_nonce("cancel_xero_import_nonce"); ?>'
                                    },
                                    success: function(response) {
                                        if (response.success) {
                                            // Show success message briefly before reload
                                            $('.xero-modal-body').html(`
                                                <div style="text-align: center; padding: 20px;">
                                                    <div style="font-size: 48px; color: #28a745; margin-bottom: 20px;">✓</div>
                                                    <p style="font-size: 18px; margin-bottom: 20px;">Import cancelled successfully</p>
                                                    <p>Reloading page...</p>
                                                </div>
                                            `);

                                            setTimeout(function() {
                                                modal.hide();
                                                location.reload();
                                            }, 1500);
                                        } else {
                                            // Show error message
                                            $('#modal-message').html(`<div class="alert alert-error">Error: ${response.data || 'Unknown error'}</div>`);
                                            $('.xero-cancel-yes').prop('disabled', false).text('Yes, Cancel Import');
                                            $('.xero-cancel-no').prop('disabled', false);
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        // Show error message
                                        $('#modal-message').html(`<div class="alert alert-error">Error cancelling import: ${error}</div>`);
                                        $('.xero-cancel-yes').prop('disabled', false).text('Yes, Cancel Import');
                                        $('.xero-cancel-no').prop('disabled', false);
                                    }
                                });
                            });
                        }

                        // Handle cancel button click
                        $('.xero-cancel-import').on('click', function() {
                            showCancelConfirmationModal();
                        });

                        // Function to check progress and update modal
                        function checkImportProgress(formId) {
                            if (isImportCancelled) {
                                return;
                            }

                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'check_xero_import_progress',
                                    form_id: formId,
                                    nonce: '<?php echo wp_create_nonce("check_xero_import_progress_nonce"); ?>'
                                },
                                success: function(response) {
                                    if (response.success && response.data) {
                                        const progress = response.data.progress;
                                        const processed = response.data.processed;
                                        const total = response.data.total;

                                        updateModalProgress(progress, processed, total);

                                        if (progress < 100 && !response.data.import_complete) {
                                            // Automatically continue the import
                                            $.ajax({
                                                url: window.location.href,
                                                type: 'POST',
                                                data: {
                                                    continue_import: formId
                                                },
                                                success: function() {
                                                    // Check progress again after a delay
                                                    setTimeout(function() {
                                                        checkImportProgress(formId);
                                                    }, 2000);
                                                },
                                                error: function(xhr, status, error) {
                                                    // Check if this is a rate limit error (status code 429)
                                                    if (xhr.status === 429) {
                                                        // Extract rate limit information from headers if available
                                                        const retryAfter = xhr.getResponseHeader('retry-after');
                                                        const rateLimitProblem = xhr.getResponseHeader('x-rate-limit-problem');

                                                        if (retryAfter) {
                                                            const waitTime = parseInt(retryAfter);
                                                            const problem = rateLimitProblem || 'unknown';

                                                            // Show rate limit information
                                                            $('#rate-limit-info').show();
                                                            $('.rate-limit-message').text(
                                                                "You've hit Xero's " + problem + " API rate limit. Please wait before making more requests."
                                                            );
                                                            $('.import-status').text('Rate limited - waiting to retry');
                                                            startRateLimitTimer(waitTime);
                                                        }
                                                    }

                                                    $('#modal-message').html(
                                                        '<div class="alert alert-error">Error continuing import: ' + error + '</div>'
                                                    );
                                                    progressBar.removeClass('processing');
                                                }
                                            });
                                        } else {
                                            // Update UI for completed import
                                            $('.xero-modal-header').addClass('import-complete');
                                            $('.xero-modal-header h3').text('Import Complete');
                                            $('.progress-bar-fill').addClass('complete');
                                            progressBar.removeClass('processing');

                                            // Show success message with notification info
                                            $('.xero-modal-body').html(`
                                                        <div style="text-align: center; padding: 20px;">
                                                            <div style="font-size: 48px; color: #28a745; margin-bottom: 20px;">✓</div>
                                                            <p style="font-size: 18px; margin-bottom: 20px;">Import completed successfully!</p>
                                                            <p style="font-size: 14px; color: #666; margin-bottom: 20px;">A summary notification has been sent to the administrator.</p>
                                                            <p>Reloading page...</p>
                                                        </div>
                                                    `);

                                            // Refresh the page after a short delay
                                            setTimeout(function() {
                                                modal.hide();
                                                location.reload();
                                            }, 2000);
                                        }
                                    }
                                },
                                error: function(xhr, status, error) {
                                    // Check if this is a rate limit error (status code 429)
                                    if (xhr.status === 429) {
                                        // Extract rate limit information from headers if available
                                        const retryAfter = xhr.getResponseHeader('retry-after');
                                        const rateLimitProblem = xhr.getResponseHeader('x-rate-limit-problem');

                                        if (retryAfter) {
                                            const waitTime = parseInt(retryAfter);
                                            const problem = rateLimitProblem || 'unknown';

                                            // Show rate limit information
                                            $('#rate-limit-info').show();
                                            $('.rate-limit-message').text(
                                                "You've hit Xero's " + problem + " API rate limit. Please wait before making more requests."
                                            );
                                            $('.import-status').text('Rate limited - waiting to retry');
                                            startRateLimitTimer(waitTime);
                                        }
                                    }

                                    $('#modal-message').html(
                                        '<div class="alert alert-error">Failed to check import progress: ' + error + '</div>'
                                    );
                                    progressBar.removeClass('processing');
                                }
                            });
                        }

                        // Function to show confirmation in modal
                        function showConfirmationModal(formElement) {
                            // Configure modal for confirmation
                            $('.xero-modal-header h3').text('Confirm Import');
                            $('.xero-modal-body').html(`
                                <div>
                                    <p style="font-size: 15px; line-height: 1.5; margin: 0;">Are you sure you want to start the import process? This may take a while depending on the amount of records.</p>
                                </div>
                                <div class="modal-actions" style="display: flex; justify-content: flex-end; gap: 12px;">
                                    <button type="button" class="button button-secondary xero-cancel-confirm">Cancel</button>
                                    <button type="button" class="button button-primary xero-confirm-sync">Start Import</button>
                                </div>
                            `);

                            // Show the modal
                            modal.show();

                            // Handle confirmation buttons
                            $('.xero-cancel-confirm').on('click', function() {
                                modal.hide();
                            });

                            $('.xero-confirm-sync').on('click', function() {
                                // Get form data
                                const $button = $(formElement).find('button[type="submit"]:not([name="cancel_import"])');
                                const formId = $button.data('form-id');

                                // Configure modal for progress tracking
                                $('.xero-modal-header h3').text('Import Progress');
                                $('.xero-modal-body').html(`
                                    <div class="xero-import-progress" style="margin-bottom: 15px;">
                                        <div class="progress-bar">
                                            <div class="progress-bar-fill">
                                                <div class="progress-bar-pulse"></div>
                                            </div>
                                        </div>
                                        <div class="progress-text" style="margin-top: 5px; font-size: 13px;">Starting import...</div>
                                    </div>
                                    <div id="rate-limit-info" style="margin-bottom: 15px; display: none;">
                                        <div class="rate-limit-alert" style="background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">
                                            <h4 style="margin-top: 0; margin-bottom: 5px;">Rate Limit Reached</h4>
                                            <p class="rate-limit-message" style="margin-bottom: 5px;"></p>
                                            <div class="rate-limit-timer" style="font-weight: bold;"></div>
                                        </div>
                                    </div>
                                    <div id="modal-message"></div>
                                    <div class="modal-actions">
                                        <button type="button" class="button button-secondary xero-cancel-import">Cancel Import</button>
                                    </div>
                                `);

                                // Reset progress bar
                                $('.xero-modal .progress-bar-fill').css('width', '0%');
                                $('#modal-message').empty();
                                modal.find('.progress-bar').addClass('processing');

                                // Reattach cancel handler
                                $('.xero-cancel-import').on('click', function() {
                                    // Show cancel confirmation in modal
                                    showCancelConfirmationModal();
                                });

                                // Function to show cancel confirmation in modal
                                function showCancelConfirmationModal() {
                                    // Save current modal content to restore if user cancels
                                    const currentModalContent = $('.xero-modal-body').html();
                                    const currentModalHeader = $('.xero-modal-header h3').text();

                                    // Configure modal for cancel confirmation
                                    $('.xero-modal-header h3').text('Confirm Cancellation');
                                    $('.xero-modal-body').html(`
                                                        <div>
                                                            <p style="font-size: 15px; line-height: 1.5; margin: 0; color: #333;">Are you sure you want to cancel the import? This cannot be undone.</p>
                                                        </div>
                                                        <div class="modal-actions" style="display: flex; justify-content: flex-end; gap: 12px;">
                                                            <button type="button" class="button button-secondary xero-cancel-no">No, Continue Import</button>
                                                            <button type="button" class="button button-danger xero-cancel-yes" style="background-color: #dc3545 !important; color: white !important; border-color: #dc3545 !important;">Yes, Cancel Import</button>
                                                        </div>
                                                    `);

                                    // Handle cancel confirmation buttons
                                    $('.xero-cancel-no').on('click', function() {
                                        // Restore previous modal content
                                        $('.xero-modal-header h3').text(currentModalHeader);
                                        $('.xero-modal-body').html(currentModalContent);

                                        // Reattach cancel handler
                                        $('.xero-cancel-import').on('click', function() {
                                            showCancelConfirmationModal();
                                        });
                                    });

                                    $('.xero-cancel-yes').on('click', function() {
                                        isImportCancelled = true;
                                        // Show loading state
                                        $(this).prop('disabled', true).text('Cancelling...');
                                        $('.xero-cancel-no').prop('disabled', true);

                                        $.ajax({
                                            url: ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'cancel_xero_import',
                                                nonce: '<?php echo wp_create_nonce("cancel_xero_import_nonce"); ?>'
                                            },
                                            success: function(response) {
                                                if (response.success) {
                                                    // Show success message briefly before reload
                                                    $('.xero-modal-body').html(`
                                                        <div style="text-align: center; padding: 20px;">
                                                            <div style="font-size: 48px; color: #28a745; margin-bottom: 20px;">✓</div>
                                                            <p style="font-size: 18px; margin-bottom: 20px;">Import cancelled successfully</p>
                                                            <p>Reloading page...</p>
                                                        </div>
                                                    `);

                                                    setTimeout(function() {
                                                        modal.hide();
                                                        location.reload();
                                                    }, 2000);
                                                } else {
                                                    // Show error message
                                                    $('#modal-message').html(`<div class="alert alert-error">Error: ${response.data || 'Unknown error'}</div>`);
                                                    $('.xero-cancel-yes').prop('disabled', false).text('Yes, Cancel Import');
                                                    $('.xero-cancel-no').prop('disabled', false);
                                                }
                                            },
                                            error: function(xhr, status, error) {
                                                // Show error message
                                                $('#modal-message').html(`<div class="alert alert-error">Error cancelling import: ${error}</div>`);
                                                $('.xero-cancel-yes').prop('disabled', false).text('Yes, Cancel Import');
                                                $('.xero-cancel-no').prop('disabled', false);
                                            }
                                        });
                                    });
                                }

                                // Submit the form via AJAX
                                $.ajax({
                                    url: window.location.href,
                                    type: 'POST',
                                    data: {
                                        sync_form: formId
                                    },
                                    success: function() {
                                        // Start checking progress after a short delay
                                        setTimeout(function() {
                                            checkImportProgress(formId);
                                        }, 2000);
                                    },
                                    error: function(xhr, status, error) {
                                        // Check if this is a rate limit error (status code 429)
                                        if (xhr.status === 429) {
                                            // Extract rate limit information from headers if available
                                            const retryAfter = xhr.getResponseHeader('retry-after');
                                            const rateLimitProblem = xhr.getResponseHeader('x-rate-limit-problem');

                                            if (retryAfter) {
                                                const waitTime = parseInt(retryAfter);
                                                const problem = rateLimitProblem || 'unknown';

                                                // Show rate limit information
                                                $('#rate-limit-info').show();
                                                $('.rate-limit-message').text(
                                                    "You've hit Xero's " + problem + " API rate limit. Please wait before making more requests."
                                                );
                                                $('.import-status').text('Rate limited - waiting to retry');
                                                startRateLimitTimer(waitTime);
                                            }
                                        }

                                        $('#modal-message').html(
                                            '<div class="alert alert-error">Failed to start import: ' + error + '</div>'
                                        );
                                        modal.find('.progress-bar').removeClass('processing');
                                    }
                                });
                            });
                        }

                        $('.xero-sync-form').on('submit', function(e) {
                            if (e.originalEvent && e.originalEvent.submitter && e.originalEvent.submitter.name === 'cancel_import') {
                                return true;
                            }

                            e.preventDefault();
                            isImportCancelled = false;

                            // Show confirmation in modal
                            showConfirmationModal(this);
                        });
                    } catch (error) {
                        console.error('Error in Xero sync button handler:', error);
                    }
                });
            })(jQuery);
        </script>
    <?php
        return ob_get_clean();
    }

    /**
     * Log a message to the error log
     *
     * @param string $message The message to log
     * @param string $type The type of message (info, warning, error, debug, success)
     * @param bool $force_log Whether to log even if not in debug mode
     */
    private function log_message($message, $type = 'info', $force_log = false)
    {
        // Don't log debug messages unless WP_DEBUG is enabled
        if (!$force_log && $type === 'debug' && (!defined('WP_DEBUG') || !WP_DEBUG)) {
            return;
        }

        // For large data arrays, truncate the output to avoid memory issues
        if (is_array($message) || is_object($message)) {
            $message = $this->truncate_large_data(print_r($message, true));
        } elseif (is_string($message) && strlen($message) > 1000) {
            $message = substr($message, 0, 997) . '...';
        }

        if (defined('WP_DEBUG') && WP_DEBUG || $force_log) {
            error_log("[XERO {$type}] {$message}");
        }
    }

    /**
     * Truncate large data arrays for logging
     *
     * @param string $data The data to truncate
     * @param int $max_length Maximum length before truncation
     * @return string Truncated data
     */
    private function truncate_large_data($data, $max_length = 1000)
    {
        if (strlen($data) <= $max_length) {
            return $data;
        }

        return substr($data, 0, $max_length - 3) . '...';
    }

    private function notify_admin($message)
    {
        // Add message to the collection instead of sending immediately
        $this->notification_messages[] = $message;

        // Log the message
        $this->log_message('Added notification: ' . $message, 'info');
    }

    /**
     * Send a consolidated email with all collected notification messages
     */
    private function send_consolidated_notifications()
    {
        if (empty($this->notification_messages)) {
            return; // No messages to send
        }

        $admin_email = get_option('admin_email');
        $subject = 'Xero Import Summary';

        // Create a summary message with all notifications
        $email_body = "Xero Import Summary\n\n";
        $email_body .= "The following events occurred during the import process:\n\n";

        // Add each notification as a bullet point
        foreach ($this->notification_messages as $index => $message) {
            $email_body .= "- " . $message . "\n";
        }

        // Add timestamp
        $email_body .= "\n\nTimestamp: " . current_time('mysql');

        // Send the consolidated email
        wp_mail($admin_email, $subject, $email_body);

        // Clear the messages after sending
        $this->notification_messages = [];

        $this->log_message('Sent consolidated notification email', 'info');
    }


    private function update_form_entries($mapping, $xero_data)
    {
        $this->log_message('Updating form entries with mapping: ' . print_r($mapping, true), 'info');
        $this->log_message('Xero data for update: ' . print_r($xero_data, true), 'info');

        $form_id = $mapping['form_id'];
        $field_mappings = json_decode($mapping['field_mappings'], true);
        $unique_id_field = $mapping['unique_id_field'];

        if (!$field_mappings) {
            $this->log_message('Invalid field mappings JSON: ' . $mapping['field_mappings'], 'error');
            return;
        }

        // Map Xero data to form fields
        $entry_data = $this->map_data_to_form_fields($xero_data, $field_mappings);

        $this->log_message('Mapped entry data: ' . print_r($entry_data, true), 'info');

        if (!$entry_data) {
            $this->log_message('No entry data mapped', 'error');
            return;
        }

        // Get the unique ID value from Xero data
        $unique_id_value = null;
        foreach ($field_mappings as $xero_field => $form_field) {
            if ($form_field == $unique_id_field) {
                // Handle nested fields
                $unique_id_value = $this->get_nested_value($xero_data, $xero_field);
                break;
            }
        }

        if (!$unique_id_value) {
            $this->log_message('Could not find unique ID value in Xero data for field: ' . $unique_id_field, 'error');
            return;
        }

        try {
            // First, check if we already have an entry with this ContactID
            global $wpdb;
            $frm_items = $wpdb->prefix . 'frm_items';
            $frm_item_metas = $wpdb->prefix . 'frm_item_metas';

            // Direct query to find exact match for the ContactID
            $exact_match_query = $wpdb->prepare(
                "SELECT fim.item_id
                FROM $frm_item_metas fim
                INNER JOIN $frm_items fi ON fi.id = fim.item_id
                WHERE fi.form_id = %d
                AND fim.field_id = %s
                AND fim.meta_value = %s
                LIMIT 1",
                $form_id,
                $unique_id_field,
                $unique_id_value
            );

            $this->log_message("Executing exact match query: " . $exact_match_query, 'debug');
            $existing_entry_id = $wpdb->get_var($exact_match_query);

            if ($existing_entry_id) {
                // We found an exact match - update this entry
                $this->log_message("Found exact match for ContactID: $unique_id_value, entry ID: $existing_entry_id", 'info');

                // Double-check the entry exists before updating
                $entry = FrmEntry::getOne($existing_entry_id);
                if ($entry) {
                    FrmEntry::update($existing_entry_id, [
                        'form_id' => $form_id,
                        'item_meta' => $entry_data
                    ]);
                    $this->log_message('Successfully updated existing entry: ' . $existing_entry_id, 'success');
                    return; // Exit early since we've updated the entry
                }
            }

            // If we get here, we didn't find an exact match
            // Let's check if there's an entry that was just created and doesn't have a ContactID yet
            // This would happen if a form was submitted and created in Xero, but the ContactID wasn't saved back

            // Look for entries that might match based on other fields like Name, Email, etc.
            $potential_matches = [];

            // Check for email match if we have email in the data
            if (isset($entry_data[$field_mappings['EmailAddress']])) {
                $email = $entry_data[$field_mappings['EmailAddress']];
                $email_query = $wpdb->prepare(
                    "SELECT fim.item_id
                    FROM $frm_item_metas fim
                    INNER JOIN $frm_items fi ON fi.id = fim.item_id
                    WHERE fi.form_id = %d
                    AND fim.field_id = %s
                    AND fim.meta_value = %s
                    LIMIT 1",
                    $form_id,
                    $field_mappings['EmailAddress'],
                    $email
                );

                $email_match = $wpdb->get_var($email_query);
                if ($email_match) {
                    $potential_matches[] = $email_match;
                    $this->log_message("Found potential match by email: $email, entry ID: $email_match", 'info');
                }
            }

            // Check for name match if we have name in the data
            if (isset($entry_data[$field_mappings['Name']])) {
                $name = $entry_data[$field_mappings['Name']];
                $name_query = $wpdb->prepare(
                    "SELECT fim.item_id
                    FROM $frm_item_metas fim
                    INNER JOIN $frm_items fi ON fi.id = fim.item_id
                    WHERE fi.form_id = %d
                    AND fim.field_id = %s
                    AND fim.meta_value = %s
                    LIMIT 1",
                    $form_id,
                    $field_mappings['Name'],
                    $name
                );

                $name_match = $wpdb->get_var($name_query);
                if ($name_match) {
                    $potential_matches[] = $name_match;
                    $this->log_message("Found potential match by name: $name, entry ID: $name_match", 'info');
                }
            }

            // If we found potential matches, check if any of them don't have a ContactID yet
            if (!empty($potential_matches)) {
                foreach ($potential_matches as $match_id) {
                    // Check if this entry already has a ContactID
                    $has_contact_id_query = $wpdb->prepare(
                        "SELECT meta_value
                        FROM $frm_item_metas
                        WHERE item_id = %d
                        AND field_id = %s
                        LIMIT 1",
                        $match_id,
                        $unique_id_field
                    );

                    $existing_contact_id = $wpdb->get_var($has_contact_id_query);

                    if (empty($existing_contact_id)) {
                        // This entry doesn't have a ContactID yet, so it's likely the one we just created
                        $this->log_message("Found entry without ContactID that matches other fields, entry ID: $match_id", 'info');

                        // Update this entry with all the data including the ContactID
                        FrmEntry::update($match_id, [
                            'form_id' => $form_id,
                            'item_meta' => $entry_data
                        ]);
                        $this->log_message('Updated existing entry with new ContactID: ' . $match_id, 'success');
                        return; // Exit early since we've updated the entry
                    }
                }
            }

            // If we get here, we didn't find any suitable existing entry to update
            // Create a new entry as a last resort, but log a warning
            $this->log_message('No matching entry found for ContactID: ' . $unique_id_value . '. Creating new entry.', 'warning');
            $new_entry_id = FrmEntry::create([
                'form_id' => $form_id,
                'item_meta' => $entry_data
            ]);
            $this->log_message('Created new entry: ' . $new_entry_id, 'success');
        } catch (Exception $e) {
            $this->log_message('Error updating/creating entry: ' . $e->getMessage(), 'error');
        }
    }

    public function handle_form_submission($entry_id, $form_id)
    {
        $this->log_message("Form submission handler triggered for entry ID: $entry_id, form ID: $form_id", 'info');

        // Ensure we have a valid access token
        if (!$this->refresh_access_token_if_needed()) {
            $this->log_message('Failed to ensure valid access token', 'error');
            return;
        }

        try {
            // Get form mappings
            $form_mappings = get_option('xero_forms_mapping', []);

            // Find matching form mapping
            $mapping = null;
            foreach ($form_mappings as $map) {
                if ($map['form_id'] == $form_id) {
                    $mapping = $map;
                    break;
                }
            }

            if (!$mapping) {
                $this->log_message("No mapping found for form ID: $form_id", 'info');
                return;
            }

            // Get entry data
            $entry = FrmEntry::getOne($entry_id, true);
            if (!$entry) {
                $this->log_message("Could not find entry with ID: $entry_id", 'error');
                return;
            }

            // Get field mappings
            $field_mappings = json_decode($mapping['field_mappings'], true);
            if (!$field_mappings) {
                $this->log_message('Invalid field mappings JSON: ' . $mapping['field_mappings'], 'error');
                return;
            }

            // Create Xero data payload
            $xero_data = [];
            $reverse_mappings = array_flip($field_mappings);

            // Check if this is an update by looking for ContactID
            $xero_id = null;
            if (isset($reverse_mappings['ContactID'])) {
                $contact_id_field = $reverse_mappings['ContactID'];
                if (isset($entry->metas[$contact_id_field])) {
                    $xero_id = $entry->metas[$contact_id_field];
                    $this->log_message("Found Xero ID for update: $xero_id", 'info');
                }
            }

            // Map form fields to Xero data
            foreach ($field_mappings as $xero_field => $form_field) {
                if (isset($entry->metas[$form_field])) {
                    $value = $entry->metas[$form_field];
                    if ($value !== null && $value !== '') {
                        $xero_data[$xero_field] = $value;
                    }
                }
            }

            $this->log_message("Prepared Xero data: " . print_r($xero_data, true), 'debug');

            // Remove concatenated fields before sending to Xero
            unset($xero_data['FirstName+LastName']);

            $this->log_message("Cleaned Xero data for sending: " . print_r($xero_data, true), 'debug');

            // Get tenant ID before making the request
            $access_token = get_option('xero_access_token');
            $tenant_id = $this->get_tenant_id($access_token);

            if (!$tenant_id) {
                throw new Exception('Could not retrieve tenant ID. Please check Xero connection.');
            }

            if ($xero_id) {
                // Update existing Xero record
                $this->log_message("Updating existing Xero record with ID: $xero_id", 'info');
                $endpoint = rtrim($mapping['endpoint'], '/') . '/' . $xero_id;
                $result = $this->update_xero_record($endpoint, $xero_data);
            } else {
                // Create new Xero record
                $this->log_message("Creating new Xero record", 'info');
                $result = $this->create_xero_record($mapping['endpoint'], $xero_data);
            }

            if (is_wp_error($result)) {
                throw new Exception($result->get_error_message());
            }

            $this->log_message("Successfully synced with Xero", 'success');

            // If we got a new Xero ID back, save it to the form entry
            if (!$xero_id && isset($result['ContactID']) && isset($reverse_mappings['ContactID'])) {
                FrmEntryMeta::update_entry_meta($entry_id, $reverse_mappings['ContactID'], null, $result['ContactID']);
                $this->log_message("Saved new Xero ID to form entry: " . $result['ContactID'], 'success');
            }
        } catch (Exception $e) {
            $this->log_message('Error in form submission handler: ' . $e->getMessage(), 'error');

            // Add error to notification messages instead of sending immediately
            $error_message = sprintf(
                "Error processing form submission - Error: %s, Form ID: %s, Entry ID: %s",
                $e->getMessage(),
                $form_id,
                $entry_id
            );
            $this->notify_admin($error_message);
        }
    }

    // Update the update_xero_record method to use PUT instead of POST
    private function update_xero_record($endpoint, $data)
    {
        if (!$this->refresh_access_token_if_needed()) {
            return new WP_Error('token_refresh_failed', 'Failed to refresh access token');
        }

        $access_token = get_option('xero_access_token');
        $tenant_id = $this->get_tenant_id($access_token);

        if (!$tenant_id) {
            return new WP_Error('no_tenant_id', 'Could not retrieve tenant ID');
        }

        // Remove concatenated fields that aren't valid Xero fields
        unset($data['FirstName+LastName']);

        $base_url = get_option('xero_api_base_url', 'https://api.xero.com/api.xro/2.0/');

        // Ensure we're using the base endpoint without the ID
        $endpoint_parts = explode('/', trim($endpoint, '/'));
        $resource_type = $endpoint_parts[0];

        $url = rtrim($base_url, '/') . '/' . $endpoint;

        // Wrap data in appropriate structure
        $payload = [
            $resource_type => [$data]
        ];

        $this->log_message("Sending PUT request to Xero endpoint: $url", 'info');
        $this->log_message("Request payload: " . json_encode($payload, JSON_PRETTY_PRINT), 'debug');

        $response = wp_remote_request($url, [
            'method' => 'POST', // Xero API uses POST for updates
            'headers' => [
                'Authorization' => 'Bearer ' . $access_token,
                'Xero-tenant-id' => $tenant_id,
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($payload)
        ]);

        if (is_wp_error($response)) {
            $this->log_message('WP Error: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        $this->log_message("Response code: $response_code", 'debug');
        $this->log_message("Response body: $response_body", 'debug');

        if ($response_code < 200 || $response_code >= 300) {
            return new WP_Error(
                'api_error',
                'Error updating record: ' . wp_remote_retrieve_response_message($response)
            );
        }

        $data = json_decode($response_body, true);

        // Extract the updated contact from the response
        if (isset($data[$resource_type][0])) {
            return $data[$resource_type][0];
        }

        return $data;
    }

    public function add_cron_interval($schedules)
    {
        $schedules['fifteen_minutes'] = array(
            'interval' => 900, // 15 minutes in seconds
            'display' => 'Every Fifteen Minutes'
        );
        return $schedules;
    }

    public function poll_xero_updates()
    {
        $this->log_message('Starting Xero polling update check', 'info');

        if (!$this->refresh_access_token_if_needed()) {
            $this->log_message('Failed to refresh access token during polling', 'error');
            return;
        }

        // Get all form mappings
        $form_mappings = get_option('xero_forms_mapping', []);

        foreach ($form_mappings as $mapping) {
            if (empty($mapping['endpoint']) || empty($mapping['form_id'])) {
                continue;
            }

            // Get the last poll time for this endpoint
            $last_poll_key = 'xero_last_poll_' . sanitize_title($mapping['endpoint']);
            $last_poll_time = get_option($last_poll_key, 0);
            $current_time = time();

            // Format the time for Xero API
            $modified_since = date('Y-m-d\TH:i:s', $last_poll_time);

            try {
                // Fetch updated records from Xero
                $updated_records = $this->fetch_xero_updates($mapping['endpoint'], $modified_since);

                if (is_wp_error($updated_records)) {
                    $this->log_message('Error fetching updates for ' . $mapping['endpoint'] . ': ' . $updated_records->get_error_message(), 'error');
                    continue;
                }

                if (!empty($updated_records)) {
                    $this->log_message('Found ' . count($updated_records) . ' updates for ' . $mapping['endpoint'], 'info');

                    foreach ($updated_records as $record) {
                        $this->update_form_entries($mapping, $record);
                    }
                }

                // Update the last poll time
                update_option($last_poll_key, $current_time);
            } catch (Exception $e) {
                $this->log_message('Error processing updates for ' . $mapping['endpoint'] . ': ' . $e->getMessage(), 'error');
            }
        }

        $this->log_message('Completed Xero polling update check', 'info');
    }

    private function fetch_xero_updates($endpoint, $modified_since)
    {
        if (!$this->refresh_access_token_if_needed()) {
            return new WP_Error('token_refresh_failed', 'Failed to refresh access token');
        }

        $access_token = get_option('xero_access_token');
        $tenant_id = $this->get_tenant_id($access_token);

        if (!$tenant_id) {
            return new WP_Error('no_tenant_id', 'Could not retrieve tenant ID');
        }

        $base_url = get_option('xero_api_base_url', 'https://api.xero.com/api.xro/2.0/');
        $url = rtrim($base_url, '/') . '/' . trim($endpoint, '/');

        // Add modified since parameter
        $url = add_query_arg('ModifiedSince', urlencode($modified_since), $url);

        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $access_token,
                'Xero-tenant-id' => $tenant_id,
                'Accept' => 'application/json',
                'If-Modified-Since' => $modified_since
            ]
        ]);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);

        // If no updates, return empty array
        if ($response_code === 304) {
            return [];
        }

        if ($response_code !== 200) {
            return new WP_Error(
                'api_error',
                'Error fetching updates: ' . wp_remote_retrieve_response_message($response)
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Extract the resource type from the endpoint URL
        $url_parts = parse_url($endpoint);
        $path_segments = explode('/', trim($url_parts['path'], '/'));
        $resource_type = end($path_segments);

        // Return the relevant data array
        return isset($data[$resource_type]) ? $data[$resource_type] : [];
    }

    private function create_xero_record($endpoint, $data)
    {
        if (!$this->refresh_access_token_if_needed()) {
            return new WP_Error('token_refresh_failed', 'Failed to refresh access token');
        }

        $access_token = get_option('xero_access_token');
        $tenant_id = $this->get_tenant_id($access_token);

        if (!$tenant_id) {
            return new WP_Error('no_tenant_id', 'Could not retrieve tenant ID');
        }

        // Remove concatenated fields that aren't valid Xero fields
        unset($data['FirstName+LastName']);

        $base_url = get_option('xero_api_base_url', 'https://api.xero.com/api.xro/2.0/');
        $url = rtrim($base_url, '/') . '/' . trim($endpoint, '/');

        // Wrap data in appropriate structure
        $resource_type = basename($endpoint);
        $payload = [
            $resource_type => [$data]
        ];

        $this->log_message("Sending request to Xero endpoint: $url", 'info');
        $this->log_message("Request payload: " . json_encode($payload, JSON_PRETTY_PRINT), 'debug');

        $response = wp_remote_post($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $access_token,
                'Xero-tenant-id' => $tenant_id,
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($payload)
        ]);

        if (is_wp_error($response)) {
            $this->log_message('WP Error: ' . $response->get_error_message(), 'error');
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        $this->log_message("Response code: $response_code", 'debug');
        $this->log_message("Response body: $response_body", 'debug');

        if ($response_code < 200 || $response_code >= 300) {
            return new WP_Error(
                'api_error',
                'Error creating record: ' . wp_remote_retrieve_response_message($response)
            );
        }

        $data = json_decode($response_body, true);

        // Extract the created contact from the response
        if (isset($data[$resource_type][0])) {
            return $data[$resource_type][0];
        }

        return $data;
    }

    private function clean_xero_data($data)
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->clean_xero_data($value);
            } else {
                if (in_array($key, ['IsSupplier', 'IsCustomer'], true)) {
                    $data[$key] = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? false;
                    error_log("[XERO debug] Cleaned {$key}: " . var_export($data[$key], true));
                    continue;
                }

                // Clean up serialized strings
                if (is_string($value)) {
                    if ($this->is_serialized($value)) {
                        $unserialized = @unserialize($value);
                        if ($unserialized !== false) {
                            if (is_array($unserialized)) {
                                if (isset($unserialized['PhoneNumber'])) {
                                    $value = $unserialized['PhoneNumber'];
                                } elseif (isset($unserialized['AddressLine1'])) {
                                    $value = $unserialized['AddressLine1'];
                                }
                            } else {
                                $value = $unserialized;
                            }
                        }
                    }

                    $value = strip_tags($value);

                    if (strpos($key, 'Phone') !== false) {
                        $value = substr($value, 0, 50);
                    } elseif (strpos($key, 'Address') !== false) {
                        $value = substr($value, 0, 500);
                    }
                }

                $data[$key] = $value;
            }
        }

        return $data;
    }


    private function set_nested_value(&$array, $path, $value)
    {
        $keys = explode('.', $path);
        $current = &$array;

        foreach ($keys as $key) {
            if (!isset($current[$key])) {
                $current[$key] = [];
            }
            $current = &$current[$key];
        }

        // Handle array values properly
        if (is_array($value)) {
            $current = $value;
        } else {
            // Convert to string to avoid type errors
            $current = strval($value);
        }
    }

    private function get_field_value($entry, $field_id)
    {
        try {
            // Simply get the value directly from metas
            if (isset($entry->metas[$field_id])) {
                $value = $entry->metas[$field_id];

                // Log the retrieved value for debugging
                $this->log_message("Retrieved value for field $field_id: " . print_r($value, true), 'debug');

                return $value;
            }

            // If the value doesn't exist, log it and return null
            $this->log_message("No value found for field ID: $field_id", 'warning');
            return null;
        } catch (Exception $e) {
            $this->log_message("Error getting field value: " . $e->getMessage(), 'error');
            return null;
        }
    }

    // First, add this method to handle the manual update action
    public function handle_manual_update()
    {
        check_admin_referer('xero_manual_update_nonce');

        $this->log_message('Starting manual update from Xero', 'info');

        // Run the polling update
        $this->poll_xero_updates();

        // Instead of redirecting, output JavaScript
    ?>
        <script type="text/javascript">
            window.location.href = '<?php echo add_query_arg('xero_message', 'update_complete', wp_get_referer()); ?>';
        </script>
        <?php
        exit;
    }

    // Add this to show admin notices
    public function show_admin_notices()
    {
        if (isset($_GET['xero_message'])) {
            switch ($_GET['xero_message']) {
                case 'update_complete':
        ?>
                    <div class="notice notice-success is-dismissible">
                        <p><?php _e('Manual update from Xero completed successfully.', 'qxero'); ?></p>
                    </div>
<?php
                    break;
            }
        }
    }



    // Add helper function to check for serialized data
    private function is_serialized($data)
    {
        // Skip if not string
        if (!is_string($data)) {
            return false;
        }
        $data = trim($data);
        if ('N;' == $data) {
            return true;
        }
        if (!preg_match('/^([adObis]):/', $data, $badions)) {
            return false;
        }
        switch ($badions[1]) {
            case 'a':
            case 'O':
            case 's':
                if (preg_match("/^{$badions[1]}:[0-9]+:.*[;}]\$/s", $data)) {
                    return true;
                }
                break;
            case 'b':
            case 'i':
            case 'd':
                if (preg_match("/^{$badions[1]}:[0-9.E-]+;\$/", $data)) {
                    return true;
                }
                break;
        }
        return false;
    }
}

// Initialize the plugin only if WordPress core is loaded
if (defined('ABSPATH')) {
    new QXero();
}

// Register activation hook to set default values
register_activation_hook(__FILE__, 'q_xero_activate');

// Register the settings on plugin initialization
add_action('admin_init', 'q_xero_register_settings');

function q_xero_register_settings()
{
    // These settings are now registered in the QXero class register_settings method
    // This function is kept for backward compatibility
}

function q_xero_activate()
{
    // Set default values for batch size and timeout if they don't exist
    if (get_option('xero_batch_size') === false) {
        update_option('xero_batch_size', 50);
    }

    if (get_option('xero_api_page_size') === false) {
        update_option('xero_api_page_size', 200);
    }

    if (get_option('xero_timeout') === false) {
        update_option('xero_timeout', 60);
    }
}

add_action('wp_ajax_check_xero_import_progress', 'check_xero_import_progress');
add_action('wp_ajax_cancel_xero_import', 'cancel_xero_import');
add_action('wp_ajax_show_cancel_confirmation', 'show_cancel_confirmation');
add_action('wp_ajax_check_xero_rate_limit', 'check_xero_rate_limit');

/**
 * AJAX handler to check the import progress
 */
function check_xero_import_progress()
{
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'check_xero_import_progress_nonce')) {
        wp_send_json_error('Security check failed');
    }

    // Get form ID
    if (!isset($_POST['form_id'])) {
        wp_send_json_error('No form ID provided');
    }

    $form_id = intval($_POST['form_id']);

    // Get import progress
    $import_progress = get_option('xero_import_progress_' . $form_id, []);

    if (empty($import_progress) || !isset($import_progress['total_records']) || !isset($import_progress['processed'])) {
        // If no import progress found, assume import is complete
        wp_send_json_success([
            'progress' => 100,
            'processed' => 0,
            'total' => 0,
            'rate_limit' => [],
            'records_synced' => 0,
            'current_batch' => 0,
            'import_complete' => true
        ]);
        return;
    }

    // Calculate progress percentage
    $progress = 0;
    if (isset($import_progress['total_records']) && isset($import_progress['processed']) && $import_progress['total_records'] > 0) {
        $progress = round(($import_progress['processed'] / $import_progress['total_records']) * 100);
    }

    // Check for rate limit status
    $rate_limit_hit = get_option('xero_rate_limit_hit', 0);
    $retry_after = get_option('xero_rate_limit_retry_after', 0);
    $rate_limit_problem = get_option('xero_rate_limit_problem', '');

    $rate_limit_info = [];
    if ($rate_limit_hit > 0) {
        $current_time = time();
        $time_passed = $current_time - $rate_limit_hit;

        if ($time_passed < $retry_after) {
            $wait_time = $retry_after - $time_passed;
            $rate_limit_info = [
                'is_rate_limited' => true,
                'wait_time' => $wait_time,
                'rate_limit_problem' => $rate_limit_problem,
                'retry_after' => $retry_after,
                'rate_limit_hit' => $rate_limit_hit
            ];
        }
    }

    wp_send_json_success([
        'progress' => $progress,
        'processed' => isset($import_progress['processed']) ? $import_progress['processed'] : 0,
        'total' => isset($import_progress['total_records']) ? $import_progress['total_records'] : 0,
        'rate_limit' => $rate_limit_info,
        'records_synced' => isset($import_progress['records_synced']) ? $import_progress['records_synced'] : 0,
        'current_batch' => isset($import_progress['current_batch']) ? $import_progress['current_batch'] : 0
    ]);
}

/**
 * AJAX handler to cancel an import
 */
function cancel_xero_import()
{
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'cancel_xero_import_nonce')) {
        wp_send_json_error('Security check failed');
    }

    // Add a cancellation message to the admin email
    $admin_email = get_option('admin_email');
    $subject = 'Xero Import Canceled';
    $message = "The Xero import process was manually canceled.\n\n";
    $message .= "Timestamp: " . current_time('mysql');

    // Send the email notification
    wp_mail($admin_email, $subject, $message);

    // Get all import progress options
    global $wpdb;
    $like_pattern = $wpdb->esc_like('xero_import_progress_') . '%';
    $options = $wpdb->get_results(
        $wpdb->prepare(
            "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE %s",
            $like_pattern
        )
    );

    // Delete all import progress options
    foreach ($options as $option) {
        delete_option($option->option_name);
    }

    // Log the cancellation
    error_log("[XERO info] Import canceled");

    wp_send_json_success(['message' => 'Import canceled successfully']);
}

/**
 * AJAX handler to check if we're currently rate limited
 */
function check_xero_rate_limit()
{
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'check_xero_rate_limit_nonce')) {
        wp_send_json_error('Security check failed');
    }

    // Check for rate limit status
    $rate_limit_hit = get_option('xero_rate_limit_hit', 0);
    $retry_after = get_option('xero_rate_limit_retry_after', 0);
    $rate_limit_problem = get_option('xero_rate_limit_problem', '');

    $rate_limit_info = [];
    if ($rate_limit_hit > 0) {
        $current_time = time();
        $time_passed = $current_time - $rate_limit_hit;

        if ($time_passed < $retry_after) {
            $wait_time = $retry_after - $time_passed;
            $rate_limit_info = [
                'is_rate_limited' => true,
                'wait_time' => $wait_time,
                'rate_limit_problem' => $rate_limit_problem,
                'retry_after' => $retry_after,
                'rate_limit_hit' => $rate_limit_hit
            ];

            // Log that we're checking and still rate limited
            error_log("[Xero info] Rate limit check: Still rate limited ($rate_limit_problem). Need to wait $wait_time more seconds.");
        } else {
            // We've waited long enough, clear the rate limit flags
            delete_option('xero_rate_limit_hit');
            delete_option('xero_rate_limit_retry_after');
            delete_option('xero_rate_limit_problem');

            $rate_limit_info = [
                'is_rate_limited' => false
            ];

            // Log that we're checking and no longer rate limited
            error_log("[Xero info] Rate limit check: No longer rate limited.");
        }
    } else {
        $rate_limit_info = [
            'is_rate_limited' => false
        ];

        // Log that we're checking and not rate limited
        error_log("[Xero info] Rate limit check: Not rate limited.");
    }

    wp_send_json_success($rate_limit_info);
}

/**
 * AJAX handler to show cancel confirmation
 */
function show_cancel_confirmation()
{
    // Verify nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'show_cancel_confirmation_nonce')) {
        wp_send_json_error('Security check failed');
    }

    $html = '<div class="confirmation-message">Are you sure you want to cancel the import? This cannot be undone.</div>' .
        '<div class="modal-buttons">' .
        '<button type="button" class="button button-primary confirm-cancel">Yes, Cancel Import</button> ' .
        '<button type="button" class="button continue-import">No, Continue Import</button>' .
        '</div>';

    wp_send_json_success(['html' => $html]);
}
